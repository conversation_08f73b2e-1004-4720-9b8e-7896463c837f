name: 🚀 Ultra-Modern CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  NODE_VERSION: '20'
  BUN_VERSION: '1.1.38'

jobs:
  # Quality checks with modern tooling
  quality:
    name: 🔍 Code Quality & Security
    runs-on: ubuntu-latest
    timeout-minutes: 10
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 🏗️ Setup Bun
        uses: oven-sh/setup-bun@v2
        with:
          bun-version: ${{ env.BUN_VERSION }}

      - name: 📦 Install dependencies
        run: bun install --frozen-lockfile

      - name: 🔍 Lint with Biome
        run: bun run lint

      - name: 🎨 Format check
        run: bun run format --check

      - name: 🔒 Security audit
        run: bun audit

      - name: 📊 Type check
        run: bun run type-check

  # Testing with comprehensive coverage
  test:
    name: 🧪 Test Suite
    runs-on: ubuntu-latest
    timeout-minutes: 15
    
    strategy:
      matrix:
        node-version: ['18', '20', '22']
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🏗️ Setup Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'npm'

      - name: 🏗️ Setup Bun
        uses: oven-sh/setup-bun@v2
        with:
          bun-version: ${{ env.BUN_VERSION }}

      - name: 📦 Install dependencies
        run: bun install --frozen-lockfile

      - name: 🧪 Run tests
        run: bun run test --coverage

      - name: 📊 Upload coverage to Codecov
        uses: codecov/codecov-action@v4
        with:
          file: ./coverage/lcov.info
          flags: unittests
          name: codecov-umbrella
          fail_ci_if_error: false

  # Performance and accessibility testing
  performance:
    name: ⚡ Performance & A11y
    runs-on: ubuntu-latest
    timeout-minutes: 20
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🏗️ Setup Bun
        uses: oven-sh/setup-bun@v2
        with:
          bun-version: ${{ env.BUN_VERSION }}

      - name: 📦 Install dependencies
        run: bun install --frozen-lockfile

      - name: 🏗️ Build application
        run: bun run build

      - name: 🚀 Start preview server
        run: bun run preview &
        
      - name: ⏳ Wait for server
        run: npx wait-on http://localhost:4173 --timeout 30000

      - name: 🔍 Lighthouse CI
        uses: treosh/lighthouse-ci-action@v12
        with:
          configPath: './.lighthouserc.json'
          uploadArtifacts: true
          temporaryPublicStorage: true

  # Build and deployment
  build:
    name: 🏗️ Build & Deploy
    runs-on: ubuntu-latest
    needs: [quality, test]
    timeout-minutes: 15
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🏗️ Setup Bun
        uses: oven-sh/setup-bun@v2
        with:
          bun-version: ${{ env.BUN_VERSION }}

      - name: 📦 Install dependencies
        run: bun install --frozen-lockfile

      - name: 🏗️ Build application
        run: bun run build

      - name: 📊 Bundle analyzer
        run: bun run build:analyze
        continue-on-error: true

      - name: 📤 Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: build-files
          path: dist/
          retention-days: 7

      - name: 🚀 Deploy to staging
        if: github.ref == 'refs/heads/develop'
        run: echo "Deploy to staging environment"
        # Add your staging deployment logic here

      - name: 🚀 Deploy to production
        if: github.ref == 'refs/heads/main'
        run: echo "Deploy to production environment"
        # Add your production deployment logic here

  # Dependency updates and security
  security:
    name: 🔒 Security Scan
    runs-on: ubuntu-latest
    if: github.event_name == 'push'
    timeout-minutes: 10
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🔍 Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: 📤 Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'

  # Notification and reporting
  notify:
    name: 📢 Notifications
    runs-on: ubuntu-latest
    needs: [quality, test, performance, build]
    if: always()
    
    steps:
      - name: 📊 Report status
        run: |
          echo "Quality: ${{ needs.quality.result }}"
          echo "Tests: ${{ needs.test.result }}"
          echo "Performance: ${{ needs.performance.result }}"
          echo "Build: ${{ needs.build.result }}"
          
      - name: 🎉 Success notification
        if: ${{ needs.quality.result == 'success' && needs.test.result == 'success' && needs.build.result == 'success' }}
        run: echo "🎉 All checks passed! Ready for deployment."
        
      - name: ❌ Failure notification
        if: ${{ needs.quality.result == 'failure' || needs.test.result == 'failure' || needs.build.result == 'failure' }}
        run: echo "❌ Some checks failed. Please review the logs."
