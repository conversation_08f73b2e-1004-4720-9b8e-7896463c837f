{"root": ["./src/App.tsx", "./src/main.tsx", "./src/vite-env.d.ts", "./src/components/Auth.tsx", "./src/components/CommandPalette.tsx", "./src/components/ErrorBoundary.tsx", "./src/components/Footer.tsx", "./src/components/Hero.tsx", "./src/components/IconRenderer.tsx", "./src/components/InnovationLab.tsx", "./src/components/JoinModal.tsx", "./src/components/LoadingScreen.tsx", "./src/components/Mission.tsx", "./src/components/NetworkAnimation.tsx", "./src/components/PricingCard.tsx", "./src/components/Services.tsx", "./src/components/ToolCard.tsx", "./src/components/ValuesCarousel.tsx", "./src/components/ui/accordion.tsx", "./src/components/ui/alert-dialog.tsx", "./src/components/ui/alert.tsx", "./src/components/ui/aspect-ratio.tsx", "./src/components/ui/avatar.tsx", "./src/components/ui/badge.tsx", "./src/components/ui/breadcrumb.tsx", "./src/components/ui/button.tsx", "./src/components/ui/calendar.tsx", "./src/components/ui/card.tsx", "./src/components/ui/carousel.tsx", "./src/components/ui/chart.tsx", "./src/components/ui/checkbox.tsx", "./src/components/ui/collapsible.tsx", "./src/components/ui/command.tsx", "./src/components/ui/context-menu.tsx", "./src/components/ui/custom-tooltip.tsx", "./src/components/ui/dialog.tsx", "./src/components/ui/drawer.tsx", "./src/components/ui/dropdown-menu.tsx", "./src/components/ui/form.tsx", "./src/components/ui/hover-card.tsx", "./src/components/ui/input-otp.tsx", "./src/components/ui/input.tsx", "./src/components/ui/label.tsx", "./src/components/ui/menubar.tsx", "./src/components/ui/navigation-menu.tsx", "./src/components/ui/pagination.tsx", "./src/components/ui/popover.tsx", "./src/components/ui/progress.tsx", "./src/components/ui/radio-group.tsx", "./src/components/ui/resizable.tsx", "./src/components/ui/scroll-area.tsx", "./src/components/ui/select.tsx", "./src/components/ui/separator.tsx", "./src/components/ui/sheet.tsx", "./src/components/ui/sidebar.tsx", "./src/components/ui/skeleton.tsx", "./src/components/ui/slider.tsx", "./src/components/ui/sonner.tsx", "./src/components/ui/switch.tsx", "./src/components/ui/table.tsx", "./src/components/ui/tabs.tsx", "./src/components/ui/textarea.tsx", "./src/components/ui/toast.tsx", "./src/components/ui/toaster.tsx", "./src/components/ui/toggle-group.tsx", "./src/components/ui/toggle.tsx", "./src/components/ui/tooltip.tsx", "./src/components/ui/use-toast.ts", "./src/hooks/use-mobile.tsx", "./src/hooks/use-performance.ts", "./src/hooks/use-toast.ts", "./src/hooks/useTools.ts", "./src/layouts/AdminLayout.tsx", "./src/lib/supabase.ts", "./src/lib/utils.ts", "./src/pages/AIDevelopmentPricing.tsx", "./src/pages/AutomationPricing.tsx", "./src/pages/BinanceWidget.tsx", "./src/pages/DataSciencePricing.tsx", "./src/pages/Education.tsx", "./src/pages/Index.tsx", "./src/pages/ModernizePricing.tsx", "./src/pages/Tools.tsx", "./src/pages/ToolsAdmin.tsx", "./src/pages/admin/applications.tsx", "./src/services/toolsService.ts", "./src/stores/app-store.ts", "./src/types/join.ts", "./src/types/tools.ts"], "version": "5.6.3"}