import React, { useState, useMemo, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ArrowLeft, Code2, Brain, Terminal, Palette, Database, Lock, Globe2, Search, Star, Share2, Tags, TrendingUp, Users, Award, Plus } from 'lucide-react';
import { Link, useNavigate } from 'react-router-dom';
import { useTools } from '../hooks/useTools';
import ToolCard from '../components/ToolCard';
import IconRenderer from '../components/IconRenderer';
import LoadingScreen from '../components/LoadingScreen';
import { useVirtualizer } from '@tanstack/react-virtual';
import { useMeasure } from 'react-use';

interface ToolRating {
  score: number;
  votes: number;
}

interface ToolTags {
  primary: string;
  others: string[];
}

interface Tool {
  name: string;
  description: string;
  url: string;
  rating?: ToolRating;
  tags?: ToolTags;
  featured?: boolean;
  downloads?: number;
}

interface Category {
  title: string;
  icon: JSX.Element;
  description: string;
  tools: Tool[];
}

const StatsCard = ({ icon, label, value }: { icon: JSX.Element; label: string; value: string }) => (
  <div className="bg-white/[0.02] rounded-xl p-4 border border-white/10">
    <div className="flex items-center gap-3">
      <div className="p-2 bg-emerald-500/10 rounded-lg text-emerald-500">
        {icon}
      </div>
      <div>
        <p className="text-sm text-snow/70">{label}</p>
        <p className="text-xl font-semibold text-snow">{value}</p>
      </div>
    </div>
  </div>
);

const Tools = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [password, setPassword] = useState('');
  const { tools, categories, isLoading, updateRating } = useTools();
  const navigate = useNavigate();
  const [containerRef, { width }] = useMeasure<HTMLDivElement>();
  const scrollRef = useRef(null);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: { staggerChildren: 0.1 }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.5 }
    }
  };

  const scrollbarStyles = `
    scrollbar-thin scrollbar-track-white/5 scrollbar-thumb-emerald-500/50
    hover:scrollbar-thumb-emerald-500/70
  `;

  const renderStats = () => (
    <motion.div
      variants={itemVariants}
      className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-12"
    >
      <StatsCard
        icon={<TrendingUp className="w-5 h-5" />}
        label="Total Tools"
        value={`${tools?.length || 0}`}
      />
      <StatsCard
        icon={<Users className="w-5 h-5" />}
        label="Categories"
        value={`${categories?.length || 0}`}
      />
      <StatsCard
        icon={<Award className="w-5 h-5" />}
        label="Featured"
        value={`${tools?.filter(t => t.featured).length || 0}`}
      />
      <StatsCard
        icon={<Star className="w-5 h-5" />}
        label="Avg Rating"
        value="4.6/5"
      />
    </motion.div>
  );

  const filteredTools = useMemo(() => {
    if (!tools) return [];
    return tools.filter(tool => 
      (tool.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
       tool.description.toLowerCase().includes(searchTerm.toLowerCase())) &&
      (selectedCategory === 'All' || tool.category.title === selectedCategory)
    );
  }, [tools, searchTerm, selectedCategory]);

  const handleAuth = (e: React.FormEvent) => {
    e.preventDefault();
    if (password === '@Sainpse2222') {
      setShowAuthModal(false);
      setPassword('');
      navigate('/tools/admin');
    } else {
      alert('Invalid password');
    }
  };

  const renderManageToolsButton = () => (
    <button
      onClick={() => setShowAuthModal(true)}
      className="inline-flex items-center px-4 py-2 rounded-lg bg-emerald-500/10 
                hover:bg-emerald-500/20 text-emerald-500 transition-colors border border-emerald-500/20"
    >
      <Plus className="w-4 h-4 mr-2" />
      Manage Tools
    </button>
  );

  const renderAuthModal = () => (
    <AnimatePresence>
      {showAuthModal && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/50 flex items-center justify-center z-50"
          onClick={() => setShowAuthModal(false)}
        >
          <motion.div
            initial={{ scale: 0.95 }}
            animate={{ scale: 1 }}
            exit={{ scale: 0.95 }}
            onClick={(e) => e.stopPropagation()}
            className="bg-midnight p-6 rounded-xl border border-white/10 w-full max-w-md"
          >
            <h2 className="text-2xl font-bold mb-4">Enter pass</h2>
            <form onSubmit={handleAuth}>
              <input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Enter password..."
                className="w-full p-3 bg-white/5 rounded-lg border border-white/10 text-snow mb-4"
                autoFocus
              />
              <p className="text-sm text-snow/50 mb-4">Hint: @Sainpse...</p>
              <div className="flex justify-end gap-3">
                <button
                  type="button"
                  onClick={() => setShowAuthModal(false)}
                  className="px-4 py-2 rounded-lg bg-white/5 hover:bg-white/10 text-snow/70"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 rounded-lg bg-emerald-500/20 hover:bg-emerald-500/30 text-emerald-500"
                >
                  Confirm
                </button>
              </div>
            </form>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );

  // Calculate optimal column count based on container width
  const columnCount = useMemo(() => {
    if (width < 768) return 1;
    if (width < 1280) return 2;
    return 3;
  }, [width]);

  // Dynamic grid layout calculation
  const getGridStyles = () => ({
    gridTemplateColumns: `repeat(${columnCount}, minmax(0, 1fr))`,
    gap: '2rem',
    width: '100%',
  });

  if (isLoading) {
    return <LoadingScreen />;
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-midnight to-midnight/95 text-snow pt-24">
      {renderAuthModal()}
      <div className="fixed inset-0 bg-grid-white/[0.02] bg-[size:60px_60px] pointer-events-none" />
      <div className="container mx-auto px-4 pb-12 relative" ref={containerRef}>
        <motion.div
          initial="hidden"
          animate="visible"
          variants={containerVariants}
          className="max-w-6xl mx-auto"
        >
          <div className="flex justify-between items-center mb-8">
            <Link 
              to="/"
              className="inline-flex items-center text-emerald-500 hover:text-emerald-400 transition-colors"
            >
              <ArrowLeft className="w-5 h-5 mr-2" />
              Back to Home
            </Link>
            {renderManageToolsButton()}
          </div>

          {renderStats()}

          <motion.h1 
            variants={itemVariants}
            className="text-4xl md:text-5xl font-bold mb-6"
          >
            Free & Open Source Tools
          </motion.h1>

          <motion.p 
            variants={itemVariants}
            className="text-xl text-snow/80 mb-12"
          >
            A curated collection of powerful open-source tools to enhance your development workflow.
          </motion.p>

          {/* Search and Filter Section */}
          <motion.div 
            variants={itemVariants}
            className="mb-12 space-y-6 sticky top-0 z-10 bg-gradient-to-b from-midnight via-midnight to-midnight/95 backdrop-blur-xl py-6 -mx-4 px-4 border-b border-white/5"
          >
            {/* Search Input */}
            <div className="relative max-w-2xl">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-snow/50 w-5 h-5" />
              <input
                type="text"
                placeholder="Search tools..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-12 pr-4 py-3 bg-white/5 rounded-xl focus:outline-none focus:ring-2 focus:ring-emerald-500 
                  text-snow placeholder-snow/50 transition-all duration-300 border border-white/10 hover:border-white/20"
              />
            </div>

            {/* Category Filters */}
            <div className="flex flex-wrap gap-3">
              {categories?.map(category => (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.title)}
                  className={`px-5 py-2.5 rounded-xl transition-all duration-300 border ${
                    selectedCategory === category.title
                      ? 'bg-emerald-500/20 text-emerald-400 border-emerald-500/50'
                      : 'bg-white/5 text-snow/70 hover:bg-white/10 border-white/10 hover:border-white/20'
                  }`}
                >
                  {category.title}
                </button>
              ))}
            </div>
          </motion.div>

          {/* Tools Grid with enhanced layout */}
          <AnimatePresence>
            <motion.div
              layout
              style={getGridStyles()}
              className="grid auto-rows-fr"
            >
              {categories?.map((category) => {
                const categoryTools = filteredTools.filter(
                  tool => tool.category.id === category.id
                );

                if (categoryTools.length === 0) return null;

                return (
                  <motion.div
                    key={category.id}
                    layout
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ 
                      opacity: 1, 
                      scale: 1,
                      height: categoryTools.length > 4 ? 'auto' : '100%',
                    }}
                    exit={{ opacity: 0, scale: 0.9 }}
                    className={`
                      group
                      bg-midnight/30 backdrop-blur-xl rounded-2xl
                      border border-white/10 shadow-lg shadow-black/20
                      transition-all duration-300 ease-in-out
                      hover:border-emerald-500/20 hover:shadow-emerald-500/5
                      flex flex-col
                      min-h-[500px] 
                      ${categoryTools.length > 4 ? 'row-span-2' : ''}
                    `}
                  >
                    {/* Enhanced Header with Interactive Elements */}
                    <motion.div 
                      className="p-6 bg-white/[0.02] border-b border-white/10 backdrop-blur-xl shrink-0
                               group-hover:bg-white/[0.03] transition-all duration-300"
                      layout
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <motion.div 
                            className="p-3 bg-emerald-500/10 rounded-xl text-emerald-500 mr-4
                                     group-hover:bg-emerald-500/20 transition-all duration-300"
                            whileHover={{ scale: 1.05 }}
                          >
                            <IconRenderer name={category.icon} className="w-6 h-6" />
                          </motion.div>
                          <h2 className="text-xl font-semibold">{category.title}</h2>
                        </div>
                        <span className="text-snow/50 text-sm">
                          {categoryTools.length} tools
                        </span>
                      </div>
                      <p className="text-sm text-snow/70 mt-2">{category.description}</p>
                    </motion.div>

                    {/* Dynamic Content Area */}
                    <motion.div 
                      className="flex-grow overflow-y-auto scrollbar-thin scrollbar-track-white/5 
                               scrollbar-thumb-emerald-500/50 hover:scrollbar-thumb-emerald-500/70"
                      layout
                    >
                      <div className="p-6 pt-4 space-y-4">
                        <AnimatePresence mode="popLayout">
                          {categoryTools.map((tool) => (
                            <motion.div
                              key={tool.id}
                              layout
                              initial={{ opacity: 0, y: 20 }}
                              animate={{ opacity: 1, y: 0 }}
                              exit={{ opacity: 0, y: -20 }}
                              transition={{ duration: 0.2 }}
                            >
                              <ToolCard
                                tool={tool}
                                onRate={(score) => updateRating.mutate({ toolId: tool.id, score })}
                              />
                            </motion.div>
                          ))}
                        </AnimatePresence>
                      </div>
                    </motion.div>
                  </motion.div>
                );
              })}
            </motion.div>
          </AnimatePresence>

          {filteredTools.length === 0 && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="text-center text-snow/70 py-16 bg-midnight/30 rounded-2xl border border-white/10"
            >
              <p className="text-xl">No tools found matching your search criteria.</p>
            </motion.div>
          )}
        </motion.div>
      </div>
    </div>
  );
};

export default Tools;
