import React from 'react';
import PricingCard from '../components/PricingCard';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';

const pricingPlans = [
  {
    title: 'Data Analysis',
    price: 'R3500',
    annualPrice: 'R33600',
    savePercentage: 20,
    description: 'Essential data analysis and visualization for businesses.',
    features: [
      'Data cleaning & preprocessing',
      'Basic statistical analysis',
      'Standard visualizations',
      'Monthly insights report',
      '5 hours consultation'
    ],
    featureDetails: {
      'Data cleaning & preprocessing': 'Transform raw data into analysis-ready format',
      'Basic statistical analysis': 'Discover trends and patterns in your data',
    },
    recommendation: 'Perfect for businesses starting with data analytics.'
  },
  {
    title: 'Advanced Analytics',
    price: 'R8500',
    annualPrice: 'R81600',
    savePercentage: 20,
    description: 'Advanced analytics and predictive modeling solutions.',
    features: [
      'Everything in Data Analysis, plus:',
      'Predictive modeling',
      'Machine learning implementation',
      'Interactive dashboards',
      'Automated reporting',
      '15 hours consultation'
    ],
    featureDetails: {
      'Predictive modeling': 'Forecast future trends and outcomes',
      'Interactive dashboards': 'Real-time data visualization and analysis',
    },
    recommendation: 'Ideal for data-driven decision making.',
    isPopular: true
  },
  {
    title: 'Enterprise Analytics',
    price: 'R25000',
    annualPrice: 'R240000',
    savePercentage: 20,
    description: 'Full-scale data science and ML infrastructure.',
    features: [
      'Everything in Advanced Analytics, plus:',
      'Custom ML model development',
      'Big data infrastructure setup',
      'Real-time analytics pipeline',
      'Advanced AI integration',
      'Dedicated data science team',
      '40 hours consultation'
    ],
    featureDetails: {
      'Big data infrastructure': 'Handle and process large-scale datasets',
      'Real-time analytics': 'Live data processing and insights',
    },
    recommendation: 'For organizations with complex data needs.'
  },
  {
    title: 'Research & Innovation',
    price: 'Custom',
    description: 'Cutting-edge data science research and innovation.',
    features: [
      'Novel algorithm development',
      'Research collaboration',
      'Custom infrastructure design',
      'Patent development support',
      'Unlimited consultation'
    ],
    featureDetails: {
      'Novel algorithm development': 'Create custom solutions for unique problems',
      'Research collaboration': 'Work with our data science research team',
    },
    recommendation: 'For organizations pushing data science boundaries.'
  }
];

const faqs = [
  {
    question: "What types of data can you analyze?",
    answer: "We work with various data types including structured (databases, spreadsheets), unstructured (text, images), and real-time data streams."
  },
  {
    question: "How long does implementation take?",
    answer: "Implementation time varies from 2-4 weeks for basic analytics to several months for complex enterprise solutions."
  },
  {
    question: "Do you provide data security?",
    answer: "Yes, we implement industry-standard security measures and comply with data protection regulations."
  }
];

const DataSciencePricing = () => {
  return (
    <div className="min-h-screen bg-midnight text-snow">
      <Link to="/">
        <motion.div
          className="fixed top-8 left-8 z-50 flex items-center gap-2 px-4 py-2 rounded-lg 
                     bg-white/5 hover:bg-white/10 border border-white/20 backdrop-blur-md
                     text-snow/80 hover:text-emerald-500 transition-colors"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <ArrowLeft className="w-4 h-4" />
          <span>Back to Home</span>
        </motion.div>
      </Link>

      <div className="py-20 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h1 className="text-5xl md:text-6xl font-bold mb-6">
              <span className="bg-gradient-to-r from-emerald-400 to-emerald-600 text-transparent bg-clip-text">
                Data Science Solutions
              </span>
            </h1>
            <p className="text-xl text-snow/80 max-w-3xl mx-auto">
              Transform your raw data into actionable insights with our advanced analytics solutions.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {pricingPlans.map((plan, index) => (
              <motion.div
                key={plan.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <PricingCard {...plan} />
              </motion.div>
            ))}
          </div>

          <div className="mt-32">
            <h2 className="text-3xl font-bold text-center mb-12">
              <span className="bg-gradient-to-r from-emerald-400 to-emerald-600 text-transparent bg-clip-text">
                Frequently Asked Questions
              </span>
            </h2>
            <div className="max-w-3xl mx-auto space-y-6">
              {faqs.map((faq, index) => (
                <div 
                  key={index} 
                  className="bg-white/5 backdrop-blur-md border border-white/20 rounded-lg p-6 hover:bg-white/10 transition-colors"
                >
                  <h3 className="text-xl font-semibold mb-3 text-emerald-500">{faq.question}</h3>
                  <p className="text-snow/80">{faq.answer}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DataSciencePricing;
