import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Plus, ArrowLeft, Trash2 } from 'lucide-react';
import { useTools } from '../hooks/useTools';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Textarea } from '../components/ui/textarea';
import { toast } from '../components/ui/use-toast';
import { icons } from 'lucide-react';
import IconRenderer from '../components/IconRenderer';

const ToolsAdmin = () => {
  const navigate = useNavigate();
  const { categories, addTool, addCategory, deleteTool, deleteCategory } = useTools();
  const [newTool, setNewTool] = useState({
    name: '',
    description: '',
    url: '',
    category_id: '',
    tags: [] as string[],
    featured: false
  });
  const [newCategory, setNewCategory] = useState({
    title: '',
    icon: '',
    description: '',
    order_field: 0  // Changed from order to order_field
  });
  const [iconPreview, setIconPreview] = useState<string>('');
  const availableIcons = Object.keys(icons);

  const handleAddTool = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await addTool.mutateAsync(newTool);
      setNewTool({
        name: '',
        description: '',
        url: '',
        category_id: '',
        tags: [],
        featured: false
      });
      toast({
        title: 'Success',
        description: 'Tool added successfully',
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to add tool',
        variant: 'destructive',
      });
    }
  };

  const handleAddCategory = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await addCategory.mutateAsync(newCategory);
      setNewCategory({
        title: '',
        icon: '',
        description: '',
        order_field: 0
      });
      toast({
        title: 'Success',
        description: 'Category added successfully',
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to add category',
        variant: 'destructive',
      });
    }
  };

  return (
    <div className="min-h-screen bg-midnight text-snow p-8">
      <Button
        variant="ghost"
        onClick={() => navigate('/tools')}
        className="mb-8"
      >
        <ArrowLeft className="w-4 h-4 mr-2" />
        Back to Tools
      </Button>

      <div className="max-w-6xl mx-auto grid md:grid-cols-2 gap-8">
        {/* Add Category Form */}
        <div className="bg-white/5 rounded-xl p-6 border border-white/10">
          <h2 className="text-2xl font-bold mb-6">Add Category</h2>
          <form onSubmit={handleAddCategory} className="space-y-4">
            <div>
              <label className="block text-sm mb-2">Title</label>
              <Input
                value={newCategory.title}
                onChange={(e) => setNewCategory({ ...newCategory, title: e.target.value })}
                required
              />
            </div>
            <div>
              <label className="block text-sm mb-2">Icon</label>
              <div className="space-y-2">
                <Input
                  value={newCategory.icon}
                  onChange={(e) => {
                    const value = e.target.value;
                    setNewCategory({ ...newCategory, icon: value });
                    setIconPreview(value);
                  }}
                  list="icon-suggestions"
                  placeholder="Enter Lucide icon name"
                  required
                />
                <datalist id="icon-suggestions">
                  {availableIcons.map(icon => (
                    <option key={icon} value={icon} />
                  ))}
                </datalist>
                {iconPreview && (
                  <div className="flex items-center gap-2 p-2 bg-white/5 rounded">
                    <IconRenderer name={iconPreview} className="w-5 h-5 text-emerald-500" />
                    {!icons[iconPreview as keyof typeof icons] && (
                      <span className="text-xs text-red-400">Icon not found</span>
                    )}
                  </div>
                )}
                <p className="text-xs text-snow/60">
                  Use Lucide icon names from{' '}
                  <a 
                    href="https://lucide.dev/icons" 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-emerald-500 hover:underline"
                  >
                    lucide.dev/icons
                  </a>
                </p>
              </div>
            </div>
            <div>
              <label className="block text-sm mb-2">Description</label>
              <Textarea
                value={newCategory.description}
                onChange={(e) => setNewCategory({ ...newCategory, description: e.target.value })}
                required
              />
            </div>
            <div>
              <label className="block text-sm mb-2">Order</label>
              <Input
                type="number"
                value={newCategory.order_field}
                onChange={(e) => setNewCategory({ 
                  ...newCategory, 
                  order_field: parseInt(e.target.value) 
                })}
                required
              />
            </div>
            <Button type="submit" className="w-full">
              <Plus className="w-4 h-4 mr-2" />
              Add Category
            </Button>
          </form>
        </div>

        {/* Add Tool Form */}
        <div className="bg-white/5 rounded-xl p-6 border border-white/10">
          <h2 className="text-2xl font-bold mb-6">Add Tool</h2>
          <form onSubmit={handleAddTool} className="space-y-4">
            <div>
              <label className="block text-sm mb-2">Name</label>
              <Input
                value={newTool.name}
                onChange={(e) => setNewTool({ ...newTool, name: e.target.value })}
                required
              />
            </div>
            <div>
              <label className="block text-sm mb-2">Description</label>
              <Textarea
                value={newTool.description}
                onChange={(e) => setNewTool({ ...newTool, description: e.target.value })}
                required
              />
            </div>
            <div>
              <label className="block text-sm mb-2">URL</label>
              <Input
                type="url"
                value={newTool.url}
                onChange={(e) => setNewTool({ ...newTool, url: e.target.value })}
                required
              />
            </div>
            <div>
              <label className="block text-sm mb-2">Category</label>
              <select
                value={newTool.category_id}
                onChange={(e) => setNewTool({ ...newTool, category_id: e.target.value })}
                className="w-full bg-white/5 border border-white/10 rounded-lg p-2"
                required
              >
                <option value="">Select category</option>
                {categories?.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.title}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm mb-2">Tags (comma-separated)</label>
              <Input
                value={newTool.tags.join(', ')}
                onChange={(e) => setNewTool({ ...newTool, tags: e.target.value.split(',').map(t => t.trim()) })}
              />
            </div>
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={newTool.featured}
                onChange={(e) => setNewTool({ ...newTool, featured: e.target.checked })}
                className="rounded border-white/20 bg-white/5"
              />
              <label className="text-sm">Featured</label>
            </div>
            <Button type="submit" className="w-full">
              <Plus className="w-4 h-4 mr-2" />
              Add Tool
            </Button>
          </form>
        </div>
      </div>
    </div>
  );
};

export default ToolsAdmin;
