import { Widget, unloadWidget } from 'binance-fiat-widget';
import { useEffect, useRef } from 'react';

const BinanceWidget = () => {
  const widgetRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (widgetRef.current) {
      Widget(widgetRef.current);
    }
    return () => unloadWidget();
  }, []);

  return <div id="binance-fiat-widget" ref={widgetRef}></div>;
};

export default BinanceWidget;
