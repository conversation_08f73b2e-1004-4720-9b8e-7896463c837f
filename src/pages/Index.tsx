import React, { useState, useEffect, useRef } from 'react';
import { createClient } from '@supabase/supabase-js';
import { motion, AnimatePresence, useScroll, useTransform, useSpring, useMotionValue, useVelocity } from 'framer-motion';
import { <PERSON>perture, Sparkles, Zap, Brain, Eye, Layers, Orbit, Atom } from 'lucide-react';
import Hero from '../components/Hero';
import Mission from '../components/Mission';
import ValuesCarousel from '../components/ValuesCarousel';
import Services from '../components/Services';
import TrendingRepos from '../components/TrendingRepos';
import Footer from '../components/Footer';
import { Auth } from '../components/Auth';
import { Dialog, DialogContent } from '../components/ui/dialog';
import { useNavigate, Link } from 'react-router-dom';

const supabase = createClient(
  import.meta.env.VITE_SUPABASE_URL,
  import.meta.env.VITE_SUPABASE_ANON_KEY
);

const Index = () => {
  const [showAuth, setShowAuth] = useState(false);
  const [user, setUser] = useState(null);
  const [profile, setProfile] = useState(null);
  const [authLoading, setAuthLoading] = useState(true);
  const [menuOpen, setMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isHovering, setIsHovering] = useState(false);
  const navigate = useNavigate();

  // Advanced motion values for immersive effects
  const containerRef = useRef(null);
  const { scrollY, scrollYProgress } = useScroll();
  const mouseX = useMotionValue(0);
  const mouseY = useMotionValue(0);
  const mouseXSpring = useSpring(mouseX, { stiffness: 500, damping: 100 });
  const mouseYSpring = useSpring(mouseY, { stiffness: 500, damping: 100 });

  // Parallax transforms
  const backgroundY = useTransform(scrollY, [0, 1000], [0, -200]);
  const floatingElementsY = useTransform(scrollY, [0, 1000], [0, 100]);
  const headerOpacity = useTransform(scrollYProgress, [0, 0.1], [1, 0.8]);

  useEffect(() => {
    const fetchUser = async () => {
      try {
        const { data: { user } } = await supabase.auth.getUser();
        if (user) {
          setUser(user);
          const { data: profile } = await supabase
            .from('profiles')
            .select('*')
            .eq('id', user.id)
            .single();
          setProfile(profile);
        }
      } catch (error) {
        console.error('Error fetching user:', error);
      } finally {
        setAuthLoading(false);
      }
    };

    fetchUser();

    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      if (session?.user) {
        setUser(session.user);
      } else {
        setUser(null);
        setProfile(null);
      }
    });

    return () => subscription.unsubscribe();
  }, []);

  useEffect(() => {
    const handleScroll = () => setIsScrolled(window.scrollY > 50);
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Enhanced mouse tracking for magnetic effects
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      const { clientX, clientY } = e;
      setMousePosition({ x: clientX, y: clientY });
      mouseX.set(clientX);
      mouseY.set(clientY);
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, [mouseX, mouseY]);

  const navLinks = [
    { href: '#mission', label: 'About Us' },
    { href: '#services', label: 'Services' },
    { href: '#trending-repos', label: 'AI Trends' },
    { href: '/education', label: 'Education' },
    { href: '#footer', label: 'Contact Us' },
  ];

  const headerVariants = {
    hidden: {
      y: -100,
      opacity: 0
    },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.8,
        ease: [0.22, 1, 0.36, 1],
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: -20, opacity: 0 },
    visible: { 
      y: 0, 
      opacity: 1,
      transition: {
        duration: 0.5,
        ease: "easeOut"
      }
    }
  };

  const logoVariants = {
    hidden: { scale: 0.8, opacity: 0 },
    visible: { 
      scale: 1, 
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 200,
        damping: 15
      }
    }
  };

  const letterAnimation = {
    rest: {
      y: 0,
      transition: {
        duration: 0.1,
        type: "tween",
        ease: "easeIn"
      }
    },
    hover: {
      y: -3,
      transition: {
        duration: 0.1,
        type: "tween",
        ease: "easeOut"
      }
    }
  };

  const handleNavClick = (e: React.MouseEvent<HTMLAnchorElement>, href: string) => {
    e.preventDefault();
    setMenuOpen(false); // Close mobile menu

    // Check if the href is a hash link (internal anchor)
    if (href.startsWith('#')) {
      const element = document.querySelector(href);
      if (element) {
        setTimeout(() => {
          const offsetTop = element.getBoundingClientRect().top + window.pageYOffset - 100;
          window.scrollTo({
            top: offsetTop,
            behavior: 'smooth'
          });
        }, 150);
      }
    } else {
      // Navigate to the route
      navigate(href);
    }
  };

  return (
    <div ref={containerRef} className="min-h-screen bg-midnight text-snow overflow-hidden">
      {/* Floating Ambient Particles */}
      <motion.div
        className="fixed inset-0 pointer-events-none z-0"
        style={{ y: floatingElementsY }}
      >
        {[...Array(12)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-accent/30 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [0, -20, 0],
              opacity: [0.3, 0.8, 0.3],
              scale: [1, 1.2, 1],
            }}
            transition={{
              duration: 3 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
          />
        ))}
      </motion.div>

      {/* Revolutionary Floating Navigation */}
      <motion.div
        initial="hidden"
        animate="visible"
        variants={headerVariants}
        className={`fixed top-6 left-1/2 transform -translate-x-1/2 z-50 transition-all duration-500 ${
          isScrolled
            ? 'bg-midnight/80 backdrop-blur-xl border border-accent/20 shadow-2xl shadow-accent/10'
            : 'bg-midnight/60 backdrop-blur-lg border border-accent/10'
        } rounded-2xl px-6 py-3`}
        style={{ opacity: headerOpacity }}
        whileHover={{ scale: 1.02, y: -2 }}
      >
        <div className="flex items-center justify-between w-full max-w-6xl mx-auto">
          {/* Revolutionary Logo with Morphing Effects */}
          <motion.div
            variants={logoVariants}
            className="flex items-center group cursor-pointer"
            onHoverStart={() => setIsHovering(true)}
            onHoverEnd={() => setIsHovering(false)}
            whileHover={{ scale: 1.05 }}
          >
            <motion.div className="relative mr-3">
              <motion.img
                src="/latest_logo2_black_NObg.png"
                alt="Sainpse Logo"
                className="w-8 h-8 relative z-10"
                animate={{
                  rotate: isHovering ? 360 : 0,
                  scale: isHovering ? 1.1 : 1,
                }}
                transition={{ duration: 0.8, ease: "easeInOut" }}
              />
              <motion.div
                className="absolute inset-0 bg-accent/20 rounded-full blur-md"
                animate={{
                  scale: isHovering ? 1.5 : 0,
                  opacity: isHovering ? 1 : 0,
                }}
                transition={{ duration: 0.3 }}
              />
            </motion.div>
            <motion.h1
              className="text-xl font-bold tracking-tight bg-gradient-to-r from-snow via-accent/80 to-snow bg-clip-text text-transparent"
              whileHover="hover"
              initial="rest"
            >
              {/* Enhanced letter animation with stagger */}
              {"Sainpse".split("").map((letter, index) => (
                <motion.span
                  key={index}
                  variants={letterAnimation}
                  style={{ display: "inline-block" }}
                  custom={index}
                  whileHover={{
                    y: -3,
                    color: "#38ADA9",
                    textShadow: "0 0 8px rgba(56, 173, 169, 0.6)",
                  }}
                  transition={{ delay: index * 0.05 }}
                >
                  {letter}
                </motion.span>
              ))}
            </motion.h1>
          </motion.div>

          {/* Magnetic Navigation Links */}
          <nav className="hidden md:flex items-center space-x-1">
            {navLinks.map((link, index) => (
              <motion.div
                key={link.href}
                className="relative"
                whileHover={{ scale: 1.05 }}
                style={{
                  x: useTransform(mouseXSpring, (x) => {
                    const rect = typeof window !== 'undefined' ? document.querySelector(`[data-nav="${link.href}"]`)?.getBoundingClientRect() : null;
                    if (!rect) return 0;
                    const distance = Math.abs(x - (rect.left + rect.width / 2));
                    return distance < 100 ? (x - (rect.left + rect.width / 2)) * 0.1 : 0;
                  }),
                }}
              >
                {link.href.startsWith('#') ? (
                  <motion.a
                    data-nav={link.href}
                    variants={itemVariants}
                    href={link.href}
                    onClick={(e) => handleNavClick(e, link.href)}
                    className="group relative px-4 py-2 text-sm font-medium transition-all duration-300 hover:text-accent rounded-lg hover:bg-accent/10 backdrop-blur-sm border border-transparent hover:border-accent/20"
                    custom={index}
                  >
                    <motion.span
                      className="inline-block relative z-10"
                      whileHover={{
                        y: -1,
                        textShadow: "0 0 8px rgba(56, 173, 169, 0.6)",
                      }}
                      transition={{ duration: 0.2 }}
                    >
                      {link.label}
                    </motion.span>
                    <motion.div
                      className="absolute inset-0 bg-gradient-to-r from-accent/0 via-accent/10 to-accent/0 rounded-lg opacity-0 group-hover:opacity-100"
                      initial={{ scale: 0.8 }}
                      whileHover={{ scale: 1 }}
                      transition={{ duration: 0.3 }}
                    />
                  </motion.a>
                ) : (
                  <motion.div variants={itemVariants} custom={index}>
                    <Link
                      data-nav={link.href}
                      to={link.href}
                      className="group relative px-4 py-2 text-sm font-medium transition-all duration-300 hover:text-accent rounded-lg hover:bg-accent/10 backdrop-blur-sm border border-transparent hover:border-accent/20"
                    >
                      <motion.span
                        className="inline-block relative z-10"
                        whileHover={{
                          y: -1,
                          textShadow: "0 0 8px rgba(56, 173, 169, 0.6)",
                        }}
                        transition={{ duration: 0.2 }}
                      >
                        {link.label}
                      </motion.span>
                      <motion.div
                        className="absolute inset-0 bg-gradient-to-r from-accent/0 via-accent/10 to-accent/0 rounded-lg opacity-0 group-hover:opacity-100"
                        initial={{ scale: 0.8 }}
                        whileHover={{ scale: 1 }}
                        transition={{ duration: 0.3 }}
                      />
                    </Link>
                  </motion.div>
                )}
              </motion.div>
            ))}
          </nav>

          {/* Revolutionary Mobile Menu Button */}
          <motion.button
            variants={itemVariants}
            className="md:hidden relative p-2 text-snow focus:outline-none group"
            onClick={() => setMenuOpen(!menuOpen)}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.95 }}
          >
            <motion.div
              className="absolute inset-0 bg-accent/20 rounded-lg opacity-0 group-hover:opacity-100"
              initial={{ scale: 0.8 }}
              whileHover={{ scale: 1 }}
              transition={{ duration: 0.2 }}
            />
            <motion.div
              className="relative z-10"
              animate={{ rotate: menuOpen ? 180 : 0 }}
              transition={{ duration: 0.3 }}
            >
              {menuOpen ? (
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ duration: 0.2 }}
                >
                  <Zap className="w-5 h-5" />
                </motion.div>
              ) : (
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ duration: 0.2 }}
                >
                  <Layers className="w-5 h-5" />
                </motion.div>
              )}
            </motion.div>
          </motion.button>
        </div>

        {/* Revolutionary Mobile Menu */}
        <AnimatePresence>
          {menuOpen && (
            <motion.div
              initial={{ opacity: 0, scale: 0.95, y: -20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.95, y: -20 }}
              transition={{ duration: 0.4, ease: [0.22, 1, 0.36, 1] }}
              className="absolute top-full left-0 right-0 mt-4 mx-4 md:hidden bg-midnight/90 backdrop-blur-xl border border-accent/20 rounded-2xl shadow-2xl shadow-accent/10 overflow-hidden"
            >
              <motion.div
                className="absolute inset-0 bg-gradient-to-br from-accent/5 via-transparent to-accent/5"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.2 }}
              />
              <nav className="relative z-10 p-6">
                <motion.div className="grid gap-2">
                  {navLinks.map((link, index) => (
                    <motion.div
                      key={link.href}
                      initial={{ x: -30, opacity: 0 }}
                      animate={{ x: 0, opacity: 1 }}
                      transition={{
                        delay: index * 0.1 + 0.2,
                        duration: 0.5,
                        ease: [0.22, 1, 0.36, 1]
                      }}
                    >
                      {link.href.startsWith('#') ? (
                        <motion.a
                          href={link.href}
                          onClick={(e) => handleNavClick(e, link.href)}
                          className="group relative flex items-center px-4 py-3 text-base font-medium transition-all duration-300 hover:text-accent rounded-xl hover:bg-accent/10 backdrop-blur-sm border border-transparent hover:border-accent/20"
                          whileHover={{ x: 4, scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                        >
                          <motion.div
                            className="w-2 h-2 bg-accent/40 rounded-full mr-3 group-hover:bg-accent"
                            whileHover={{ scale: 1.5 }}
                            transition={{ duration: 0.2 }}
                          />
                          <span className="relative z-10">{link.label}</span>
                          <motion.div
                            className="absolute inset-0 bg-gradient-to-r from-accent/0 via-accent/5 to-accent/0 rounded-xl opacity-0 group-hover:opacity-100"
                            initial={{ scale: 0.8 }}
                            whileHover={{ scale: 1 }}
                            transition={{ duration: 0.3 }}
                          />
                        </motion.a>
                      ) : (
                        <Link
                          to={link.href}
                          className="group relative flex items-center px-4 py-3 text-base font-medium transition-all duration-300 hover:text-accent rounded-xl hover:bg-accent/10 backdrop-blur-sm border border-transparent hover:border-accent/20"
                        >
                          <motion.div
                            className="w-2 h-2 bg-accent/40 rounded-full mr-3 group-hover:bg-accent"
                            whileHover={{ scale: 1.5 }}
                            transition={{ duration: 0.2 }}
                          />
                          <span className="relative z-10">{link.label}</span>
                          <motion.div
                            className="absolute inset-0 bg-gradient-to-r from-accent/0 via-accent/5 to-accent/0 rounded-xl opacity-0 group-hover:opacity-100"
                            initial={{ scale: 0.8 }}
                            whileHover={{ scale: 1 }}
                            transition={{ duration: 0.3 }}
                          />
                        </Link>
                      )}
                    </motion.div>
                  ))}
                </motion.div>
              </nav>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
      {/* Revolutionary Main Content with Floating Elements */}
      <motion.div
        className="relative pt-24"
        style={{ y: backgroundY }}
      >
        {/* Floating Geometric Elements */}
        <motion.div className="fixed top-1/4 right-10 w-20 h-20 pointer-events-none z-10">
          <motion.div
            className="w-full h-full border-2 border-accent/20 rounded-full"
            animate={{
              rotate: 360,
              scale: [1, 1.1, 1],
            }}
            transition={{
              rotate: { duration: 20, repeat: Infinity, ease: "linear" },
              scale: { duration: 4, repeat: Infinity, ease: "easeInOut" },
            }}
          />
          <motion.div
            className="absolute inset-4 bg-accent/10 rounded-full"
            animate={{
              rotate: -360,
              opacity: [0.3, 0.7, 0.3],
            }}
            transition={{
              rotate: { duration: 15, repeat: Infinity, ease: "linear" },
              opacity: { duration: 3, repeat: Infinity, ease: "easeInOut" },
            }}
          />
        </motion.div>

        <motion.div className="fixed top-1/2 left-10 w-16 h-16 pointer-events-none z-10">
          <motion.div
            className="w-full h-full bg-gradient-to-br from-accent/20 to-transparent rounded-lg"
            animate={{
              rotate: [0, 45, 0],
              scale: [1, 1.2, 1],
            }}
            transition={{
              duration: 6,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          />
        </motion.div>

        {/* Enhanced Content Sections */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1, delay: 0.5 }}
        >
          <Hero />
        </motion.div>

        <motion.div
          id="services"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, margin: "-100px" }}
          transition={{ duration: 0.8, ease: [0.22, 1, 0.36, 1] }}
        >
          <Services />
        </motion.div>

        <motion.div
          id="trending-repos"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, margin: "-100px" }}
          transition={{ duration: 0.8, ease: [0.22, 1, 0.36, 1], delay: 0.2 }}
        >
          <TrendingRepos />
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, margin: "-100px" }}
          transition={{ duration: 0.8, ease: [0.22, 1, 0.36, 1], delay: 0.1 }}
        >
          <ValuesCarousel />
        </motion.div>

        <motion.div
          id="mission"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, margin: "-100px" }}
          transition={{ duration: 0.8, ease: [0.22, 1, 0.36, 1], delay: 0.3 }}
        >
          <Mission />
        </motion.div>

        <motion.div
          id="footer"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, margin: "-100px" }}
          transition={{ duration: 0.8, ease: [0.22, 1, 0.36, 1], delay: 0.4 }}
        >
          <Footer />
        </motion.div>
      </motion.div>
      <Dialog open={showAuth} onOpenChange={setShowAuth}>
        <DialogContent className="sm:max-w-[425px]">
          <Auth />
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Index;
