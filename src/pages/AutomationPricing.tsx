import React from 'react';
import PricingCard from '../components/PricingCard';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';

const pricingPlans = [
  {
    title: 'Basic Automation',
    price: 'R300',
    annualPrice: 'R240',
    savePercentage: 20,
    description: 'For small businesses looking to automate simple tasks.',
    features: [
      'Task automation',
      'Basic workflow automation',
      'Email automation',
      'Monthly reports'
    ],
    featureDetails: {
      'Task automation': 'Automate repetitive tasks to save time',
      'Basic workflow automation': 'Streamline simple workflows',
    },
    recommendation: 'Perfect for small businesses starting with automation.'
  },
  {
    title: 'Advanced Automation',
    price: 'R900',
    annualPrice: 'R720',
    savePercentage: 20,
    description: 'For growing businesses needing advanced automation solutions.',
    features: [
      'Everything in Basic, plus:',
      'Advanced workflow automation',
      'Integration with third-party apps',
      'Custom automation scripts',
      'Weekly reports'
    ],
    featureDetails: {
      'Advanced workflow automation': 'Automate complex workflows',
      'Integration with third-party apps': 'Connect with popular apps like Slack, Trello, etc.',
    },
    recommendation: 'Ideal for businesses looking to scale their automation.',
    isPopular: true
  },
  {
    title: 'Enterprise Automation',
    price: 'R2000',
    annualPrice: 'R1600',
    savePercentage: 20,
    description: 'For enterprises requiring comprehensive automation solutions.',
    features: [
      'Everything in Advanced, plus:',
      'Enterprise-grade security',
      'Dedicated support',
      'Custom automation solutions',
      'Daily reports'
    ],
    featureDetails: {
      'Enterprise-grade security': 'Top-notch security for your automation processes',
      'Dedicated support': 'Priority support for your business',
    },
    recommendation: 'Great for large enterprises with complex automation needs.'
  },
  {
    title: 'Custom Automation',
    price: 'Custom',
    description: 'For businesses needing fully tailored automation solutions.',
    features: [
      'Everything in Enterprise, plus:',
      'Fully custom automation solutions',
      'Dedicated account manager',
      '24/7 support',
      'Custom reports'
    ],
    featureDetails: {
      'Fully custom automation solutions': 'Tailored automation solutions for your business',
      'Dedicated account manager': 'Personalized support from a dedicated account manager',
    },
    recommendation: 'Designed for businesses with unique automation requirements.'
  }
];

const faqs = [
  {
    question: "What is task automation?",
    answer: "Task automation involves using software to perform repetitive tasks automatically, saving time and reducing errors."
  },
  {
    question: "Can I upgrade or downgrade my plan?",
    answer: "Yes, you can change your plan at any time. The changes will be reflected in your next billing cycle."
  },
  {
    question: "What kind of support do you offer?",
    answer: "We provide email support for all plans, with priority support and 24/7 monitoring for Custom Automation customers."
  }
];

const AutomationPricing = () => {
  return (
    <div className="min-h-screen bg-midnight text-snow">
      <Link to="/">
        <motion.div
          className="fixed top-8 left-8 z-50 flex items-center gap-2 px-4 py-2 rounded-lg 
                     bg-white/5 hover:bg-white/10 border border-white/20 backdrop-blur-md
                     text-snow/80 hover:text-emerald-500 transition-colors"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <ArrowLeft className="w-4 h-4" />
          <span>Back to Home</span>
        </motion.div>
      </Link>

      <div className="py-20 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h1 className="text-5xl md:text-6xl font-bold mb-6">
              <span className="bg-gradient-to-r from-emerald-400 to-emerald-600 text-transparent bg-clip-text">
                Automation Services Pricing
              </span>
            </h1>
            <p className="text-xl text-snow/80 max-w-3xl mx-auto">
              Automate your business processes and improve efficiency with our tailored automation solutions.
            </p>
          </div>
          
          <div className="text-center mb-12">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="inline-block bg-emerald-500/10 border border-emerald-500/20 rounded-lg px-6 py-3"
            >
              <span className="text-emerald-500 font-semibold">
                30-Day Money-Back Guarantee • No Credit Card Required
              </span>
            </motion.div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {pricingPlans.map((plan, index) => (
              <motion.div
                key={plan.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <PricingCard {...plan} />
              </motion.div>
            ))}
          </div>

          <div className="text-center mt-12 mb-32">
            <p className="text-snow/60 text-sm">
              Not sure which plan is right for you?{' '}
              <a href="#" className="text-emerald-500 hover:text-emerald-400 underline">
                Compare features in detail
              </a>
              {' '}or{' '}
              <a href="#" className="text-emerald-500 hover:text-emerald-400 underline">
                schedule a demo
              </a>
            </p>
            <p className="text-snow/60 text-sm mt-4">
              Interested in modernizing services?{' '}
              <Link to="/services/modernize" className="text-emerald-500 hover:text-emerald-400 underline">
                Learn more about our modernizing services
              </Link>
            </p>
          </div>

          <div className="mt-32">
            <h2 className="text-3xl font-bold text-center mb-12">
              <span className="bg-gradient-to-r from-emerald-400 to-emerald-600 text-transparent bg-clip-text">
                Frequently Asked Questions
              </span>
            </h2>
            <div className="max-w-3xl mx-auto space-y-6">
              {faqs.map((faq, index) => (
                <div 
                  key={index} 
                  className="bg-white/5 backdrop-blur-md border border-white/20 rounded-lg p-6 hover:bg-white/10 transition-colors"
                >
                  <h3 className="text-xl font-semibold mb-3 text-emerald-500">{faq.question}</h3>
                  <p className="text-snow/80">{faq.answer}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AutomationPricing;
