export interface ToolRating {
  score: number;
  votes: number;
  last_updated: string;
}

export interface Tool {
  id: string;
  name: string;
  description: string;
  url: string;
  category_id: string;
  rating: ToolRating;
  tags: string[];
  featured: boolean;
  downloads: number;
  created_at: string;
  updated_at: string;
}

export interface Category {
  id: string;
  title: string;
  icon: string;
  description: string;
  order_field: number;
  created_at: string;
  updated_at: string;
}

export interface ToolWithCategory extends Tool {
  category: Category;
}
