import { supabase } from '../lib/supabase';
import { Tool, Category, ToolWithCategory } from '../types/tools';

export const toolsService = {
  async getCategories(): Promise<Category[]> {
    const { data, error } = await supabase
      .from('categories')
      .select('*')
      .order('order_field');  // Changed from order to order_field
    
    if (error) throw error;
    return data;
  },

  async getTools(): Promise<ToolWithCategory[]> {
    const { data, error } = await supabase
      .from('tools')
      .select(`
        *,
        category:categories(*)
      `)
      .order('name');
    
    if (error) throw error;
    return data;
  },

  async searchTools(query: string): Promise<ToolWithCategory[]> {
    const { data, error } = await supabase
      .from('tools')
      .select(`
        *,
        category:categories(*)
      `)
      .textSearch('name', query)
      .order('name');
    
    if (error) throw error;
    return data;
  },

  async updateToolRating(toolId: string, score: number): Promise<void> {
    const { error } = await supabase
      .from('tools')
      .update({
        'rating.score': score,
        'rating.votes': supabase.raw('rating.votes + 1'),
        'rating.last_updated': new Date().toISOString()
      })
      .eq('id', toolId);

    if (error) throw error;
  },

  async addTool(tool: Omit<Tool, 'id' | 'rating' | 'created_at' | 'updated_at'>): Promise<Tool> {
    const { data, error } = await supabase
      .from('tools')
      .insert([{
        ...tool,
        rating: { score: 0, votes: 0, last_updated: new Date().toISOString() }
      }])
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },

  async addCategory(category: Omit<Category, 'id' | 'created_at' | 'updated_at'>): Promise<Category> {
    const { data, error } = await supabase
      .from('categories')
      .insert([{
        ...category,
        order_field: category.order_field  // Ensure correct field name is used
      }])
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },

  async deleteTool(id: string): Promise<void> {
    const { error } = await supabase
      .from('tools')
      .delete()
      .eq('id', id);
    
    if (error) throw error;
  },

  async deleteCategory(id: string): Promise<void> {
    const { error } = await supabase
      .from('categories')
      .delete()
      .eq('id', id);
    
    if (error) throw error;
  }
};
