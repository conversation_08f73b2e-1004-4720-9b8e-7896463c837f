import { createClient } from '@supabase/supabase-js'
import { JoinApplication } from '../types/join'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseKey) {
  throw new Error('Missing Supabase environment variables')
}

export const supabase = createClient(supabaseUrl, supabaseKey)

type JoinApplicationInput = Omit<JoinApplication, 'id' | 'created_at' | 'status'>

export async function submitJoinApplication(data: JoinApplicationInput): Promise<JoinApplication> {
  const { data: result, error } = await supabase
    .from('join_applications')
    .insert([{ ...data, status: 'pending' }])
    .select('*')
    .single()

  if (error) throw error
  return result
}

export async function getJoinApplications(): Promise<JoinApplication[]> {
  const { data, error } = await supabase
    .from('join_applications')
    .select('*')
    .order('created_at', { ascending: false })

  if (error) throw error
  return data
}
