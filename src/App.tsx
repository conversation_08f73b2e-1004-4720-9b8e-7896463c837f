import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import Tools from "./pages/Tools";
import Education from "./pages/Education";
import ModernizePricing from "./pages/ModernizePricing";
import AutomationPricing from "./pages/AutomationPricing";
import ToolsAdmin from './pages/ToolsAdmin';
import AdminLayout from './layouts/AdminLayout';
import ApplicationsAdmin from './pages/admin/applications';
import AIDevelopmentPricing from './pages/AIDevelopmentPricing';
import DataSciencePricing from './pages/DataSciencePricing';

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <Router>
        <Routes>
          <Route path="/" element={<Index />} />
          <Route path="tools">
            <Route index element={<Tools />} />
            <Route path="admin" element={<ToolsAdmin />} />
          </Route>
          <Route path="/education" element={<Education />} />
          <Route path="/services/modernize" element={<ModernizePricing />} />
          <Route path="/services/automation" element={<AutomationPricing />} />
          <Route path="/services/ai-development" element={<AIDevelopmentPricing />} />
          <Route path="/services/data-science" element={<DataSciencePricing />} />
          <Route path="/admin" element={
            <AdminLayout>
              <ApplicationsAdmin />
            </AdminLayout>
          } />
        </Routes>
      </Router>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
