import React, { useRef, useState, useEffect } from 'react';
import { motion, useScroll, useTransform, useSpring, useMotionValue } from 'framer-motion';
import { Bot, Code, Database, Network, ArrowRight, Sparkles, Zap, Brain, Cpu, Star, Layers, Orbit, Atom, Eye } from 'lucide-react';
import { Link } from 'react-router-dom';
import { Button } from './ui/button';

const services = [
  {
    icon: Bot,
    title: 'Modernize',
    description: 'Building intelligent websites and integrating AI-driven customer support agents to enhance user experience and engagement.',
    link: '/services/modernize',
    color: 'from-white/5 via-accent/10 to-white/5',
    glowColor: 'shadow-accent/10',
    features: ['AI Chatbots', 'Smart Websites', '24/7 Support'],
    gradient: 'from-accent/80 to-accent',
    accentIcon: Brain
  },
  {
    icon: Network,
    title: 'Automation',
    description: 'Automating repetitive tasks and streamlining workflows to enhance efficiency and scalability.',
    link: '/services/automation',
    color: 'from-white/5 via-accent/10 to-white/5',
    glowColor: 'shadow-accent/10',
    features: ['Workflow Automation', 'Process Optimization', 'Smart Integration'],
    gradient: 'from-accent/80 to-accent',
    accentIcon: Cpu
  },
  {
    icon: Code,
    title: 'AI Development',
    description: 'Advancing Large Language Models (LLMs) and integrating cutting-edge AI into real-world applications for transformative impact.',
    link: '/services/ai-development',
    color: 'from-white/5 via-accent/10 to-white/5',
    glowColor: 'shadow-accent/10',
    features: ['Custom LLMs', 'AI Integration', 'Model Training'],
    gradient: 'from-accent/80 to-accent',
    accentIcon: Star
  },
  {
    icon: Database,
    title: 'Data Science',
    description: 'Transforming raw data into actionable intelligence through advanced analytics and machine learning.',
    link: '/services/data-science',
    color: 'from-white/5 via-accent/10 to-white/5',
    glowColor: 'shadow-accent/10',
    features: ['Data Analytics', 'ML Solutions', 'Business Intelligence'],
    gradient: 'from-accent/80 to-accent',
    accentIcon: Layers
  },
];

const Services = () => {
  const containerRef = useRef(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [hoveredCard, setHoveredCard] = useState<number | null>(null);
  
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start end", "end start"]
  });
  
  // Advanced motion values for immersive effects
  const mouseX = useMotionValue(0);
  const mouseY = useMotionValue(0);
  const mouseXSpring = useSpring(mouseX, { stiffness: 500, damping: 100 });
  const mouseYSpring = useSpring(mouseY, { stiffness: 500, damping: 100 });
  
  const backgroundY = useTransform(scrollYProgress, [0, 1], ["0%", "30%"]);
  const headerY = useTransform(scrollYProgress, [0, 0.3], [0, -30]);
  const floatingY = useTransform(scrollYProgress, [0, 1], [0, -100]);
  
  // Mouse tracking for magnetic effects
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      const { clientX, clientY } = e;
      setMousePosition({ x: clientX, y: clientY });
      mouseX.set(clientX);
      mouseY.set(clientY);
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, [mouseX, mouseY]);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.1
      }
    }
  };

  const fadeInUp = {
    hidden: { y: 30, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: [0.22, 1, 0.36, 1]
      }
    }
  };

  const iconVariants = {
    hidden: { scale: 0, rotate: -180, opacity: 0 },
    visible: {
      scale: 1,
      rotate: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 200,
        damping: 20,
        delay: 0.2
      }
    }
  };

  const cardVariants = {
    hidden: { y: 40, opacity: 0, scale: 0.95 },
    visible: {
      y: 0,
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.6,
        ease: [0.22, 1, 0.36, 1]
      }
    }
  };

  return (
    <section 
      ref={containerRef}
      id="services" 
      className="py-20 md:py-32 bg-gradient-to-b from-midnight via-darkShade to-midnight relative overflow-hidden perspective-1000"
    >
      {/* Revolutionary 3D Background Elements */}
      <motion.div 
        className="absolute inset-0 opacity-20"
        style={{ y: backgroundY }}
      >
        <motion.div 
          className="absolute inset-0 opacity-15"
          animate={{
            background: [
              "radial-gradient(circle at 30% 30%, rgba(56, 173, 169, 0.12) 0%, transparent 60%), radial-gradient(circle at 70% 70%, rgba(56, 173, 169, 0.08) 0%, transparent 60%)",
              "radial-gradient(circle at 70% 30%, rgba(56, 173, 169, 0.12) 0%, transparent 60%), radial-gradient(circle at 30% 70%, rgba(56, 173, 169, 0.08) 0%, transparent 60%)",
              "radial-gradient(circle at 50% 50%, rgba(56, 173, 169, 0.12) 0%, transparent 60%), radial-gradient(circle at 40% 60%, rgba(56, 173, 169, 0.08) 0%, transparent 60%)",
              "radial-gradient(circle at 30% 30%, rgba(56, 173, 169, 0.12) 0%, transparent 60%), radial-gradient(circle at 70% 70%, rgba(56, 173, 169, 0.08) 0%, transparent 60%)",
            ]
          }}
          transition={{ duration: 20, repeat: Infinity }}
        />
      </motion.div>

      {/* Immersive Floating Geometric Elements */}
      <motion.div 
        className="absolute inset-0 pointer-events-none"
        style={{ y: floatingY }}
      >
        {[...Array(12)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute"
            style={{
              left: `${15 + (i * 8)}%`,
              top: `${20 + (i % 4) * 20}%`,
              x: useTransform(mouseXSpring, (x) => (x - (typeof window !== 'undefined' ? window.innerWidth : 1920) / 2) * (0.015 + (i * 0.005))),
              y: useTransform(mouseYSpring, (y) => (y - (typeof window !== 'undefined' ? window.innerHeight : 1080) / 2) * (0.015 + (i * 0.005))),
            }}
            animate={{
              rotate: i % 2 === 0 ? 360 : -360,
              scale: [1, 1.3, 1],
              opacity: [0.1, 0.4, 0.1],
            }}
            transition={{
              rotate: { duration: 25 + (i * 3), repeat: Infinity, ease: "linear" },
              scale: { duration: 5 + (i * 0.5), repeat: Infinity, ease: "easeInOut" },
              opacity: { duration: 4 + (i * 0.3), repeat: Infinity, ease: "easeInOut" },
            }}
          >
            {i % 4 === 0 && <Orbit className="w-5 h-5 text-accent/25" />}
            {i % 4 === 1 && <Atom className="w-4 h-4 text-accent/20" />}
            {i % 4 === 2 && <Eye className="w-3 h-3 text-accent/15" />}
            {i % 4 === 3 && <div className="w-2 h-2 bg-accent/20 rounded-full" />}
          </motion.div>
        ))}
      </motion.div>

      {/* Enhanced Particle System */}
      <motion.div className="absolute inset-0 pointer-events-none">
        {[...Array(15)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              x: useTransform(mouseXSpring, (x) => (x - (typeof window !== 'undefined' ? window.innerWidth : 1920) / 2) * (0.01 + Math.random() * 0.02)),
              y: useTransform(mouseYSpring, (y) => (y - (typeof window !== 'undefined' ? window.innerHeight : 1080) / 2) * (0.01 + Math.random() * 0.02)),
            }}
            animate={{
              y: [-30, 30, -30],
              opacity: [0.1, 0.5, 0.1],
              scale: [1, 1.5, 1],
            }}
            transition={{
              duration: 6 + Math.random() * 3,
              repeat: Infinity,
              delay: Math.random() * 3,
            }}
          >
            <div className="w-1 h-1 bg-accent/30 rounded-full" />
          </motion.div>
        ))}
      </motion.div>

      <div className="max-w-7xl mx-auto relative z-10 px-6 lg:px-8">
        {/* Enhanced Header */}
        <motion.div
          className="text-center mb-16 md:mb-24"
          style={{ y: headerY }}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={containerVariants}
        >
          <motion.h2 
            className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 bg-gradient-to-r from-snow via-accent/80 to-snow bg-clip-text text-transparent"
            variants={fadeInUp}
            animate={{
              backgroundPosition: ["0% 50%", "100% 50%", "0% 50%"],
            }}
            transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
            style={{ backgroundSize: "200% 200%" }}
          >
            Revolutionary AI Solutions
          </motion.h2>
          
          <motion.p 
            className="text-lg md:text-xl lg:text-2xl text-snow/60 max-w-4xl mx-auto px-4 leading-relaxed"
            variants={fadeInUp}
          >
            Empowering businesses with{' '}
            <span className="text-snow/80 font-medium">cutting-edge AI solutions</span>
            {' '}and intelligent automation.
          </motion.p>
        </motion.div>

        {/* Revolutionary 3D Services Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 md:gap-10">
          {services.map((service, index) => {
            const Icon = service.icon;
            const AccentIcon = service.accentIcon;
            return (
              <motion.div
                key={service.title}
                initial="hidden"
                whileInView="visible"
                viewport={{ once: true, margin: "-100px" }}
                variants={cardVariants}
                transition={{ delay: index * 0.15 }}
                className="group relative perspective-1000"
                onHoverStart={() => setHoveredCard(index)}
                onHoverEnd={() => setHoveredCard(null)}
              >
                {/* 3D Card Container with Magnetic Effect */}
                <motion.div
                  className="relative h-full"
                  style={{
                    x: useTransform(mouseXSpring, (x) => {
                      if (hoveredCard !== index) return 0;
                      const cardElement = document.querySelector(`[data-card="${index}"]`);
                      if (!cardElement) return 0;
                      const rect = cardElement.getBoundingClientRect();
                      const distance = Math.abs(x - (rect.left + rect.width / 2));
                      return distance < 200 ? (x - (rect.left + rect.width / 2)) * 0.15 : 0;
                    }),
                    y: useTransform(mouseYSpring, (y) => {
                      if (hoveredCard !== index) return 0;
                      const cardElement = document.querySelector(`[data-card="${index}"]`);
                      if (!cardElement) return 0;
                      const rect = cardElement.getBoundingClientRect();
                      const distance = Math.abs(y - (rect.top + rect.height / 2));
                      return distance < 200 ? (y - (rect.top + rect.height / 2)) * 0.1 : 0;
                    }),
                  }}
                  whileHover={{ 
                    y: -12, 
                    scale: 1.03,
                    rotateX: 5,
                    rotateY: hoveredCard === index ? 2 : 0,
                  }}
                  whileTap={{ scale: 0.97 }}
                  transition={{ 
                    type: "spring", 
                    stiffness: 300, 
                    damping: 30,
                    duration: 0.6 
                  }}
                >
                  <motion.div
                    data-card={index}
                    className="relative p-8 md:p-10 rounded-3xl bg-gradient-to-br from-white/8 via-white/5 to-white/3 backdrop-blur-xl border border-white/10 hover:border-accent/40 transition-all duration-700 overflow-hidden cursor-pointer h-full group shadow-2xl hover:shadow-accent/20"
                    whileHover={{
                      boxShadow: "0 25px 50px -12px rgba(56, 173, 169, 0.25), 0 0 0 1px rgba(56, 173, 169, 0.1)",
                    }}
                  >
                    {/* Revolutionary Dynamic Background */}
                    <motion.div 
                      className={`absolute inset-0 bg-gradient-to-br ${service.color} opacity-0 group-hover:opacity-100 transition-opacity duration-700`}
                      initial={{ scale: 0.8 }}
                      whileHover={{ scale: 1 }}
                      transition={{ duration: 0.5 }}
                    />
                    
                    {/* Immersive Glow Effect */}
                    <motion.div
                      className="absolute inset-0 bg-gradient-to-br from-accent/10 via-transparent to-accent/5 opacity-0 group-hover:opacity-100 rounded-3xl"
                      initial={{ opacity: 0, scale: 0.9 }}
                      whileHover={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.6 }}
                    />
                    
                    {/* Enhanced Service Content */}
                    <div className="relative z-10 h-full flex flex-col">
                      {/* Revolutionary Icon Container */}
                      <motion.div
                        className={`relative w-16 h-16 md:w-20 md:h-20 rounded-2xl bg-gradient-to-br ${service.gradient} flex items-center justify-center mb-8 shadow-xl group-hover:shadow-2xl transition-all duration-700`}
                        variants={iconVariants}
                        whileHover={{ 
                          scale: 1.1,
                          rotate: 5,
                          boxShadow: "0 20px 40px -12px rgba(56, 173, 169, 0.4)",
                        }}
                        transition={{ type: "spring", stiffness: 300, damping: 20 }}
                      >
                        <motion.div
                          className="relative z-10"
                          whileHover={{ rotate: 15, scale: 1.1 }}
                          transition={{ duration: 0.3, type: "spring" }}
                        >
                          <Icon className="w-8 h-8 md:w-10 md:h-10 text-white drop-shadow-lg" />
                        </motion.div>
                        
                        {/* Accent Icon Animation */}
                        <motion.div
                          className="absolute top-1 right-1"
                          animate={{
                            scale: hoveredCard === index ? [1, 1.2, 1] : 1,
                            rotate: hoveredCard === index ? [0, 180, 360] : 0,
                          }}
                          transition={{ duration: 2, repeat: Infinity }}
                        >
                          <AccentIcon className="w-3 h-3 text-accent/60" />
                        </motion.div>
                      </motion.div>
                      
                      {/* Revolutionary Title with 3D Effect */}
                      <motion.h3 
                        className="text-xl md:text-2xl font-bold mb-4 text-snow group-hover:text-accent transition-all duration-500"
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.2 + index * 0.1 }}
                        whileHover={{
                          textShadow: "0 0 20px rgba(56, 173, 169, 0.6)",
                          scale: 1.02,
                        }}
                      >
                        {service.title}
                      </motion.h3>

                      {/* Enhanced Description */}
                      <motion.p 
                        className="text-sm md:text-base text-snow/70 mb-6 leading-relaxed group-hover:text-snow/90 transition-colors duration-500"
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.3 + index * 0.1 }}
                      >
                        {service.description}
                      </motion.p>

                      {/* Revolutionary Feature Tags */}
                      <motion.div 
                        className="flex flex-wrap gap-2 mb-6"
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.4 + index * 0.1 }}
                      >
                        {service.features.map((feature, featureIndex) => (
                          <motion.span
                            key={feature}
                            className="px-3 py-1 text-xs font-medium bg-accent/10 text-accent rounded-full border border-accent/20 group-hover:bg-accent/20 group-hover:border-accent/40 transition-all duration-500"
                            whileHover={{ 
                              scale: 1.05,
                              backgroundColor: "rgba(56, 173, 169, 0.3)",
                            }}
                            transition={{ delay: featureIndex * 0.1 }}
                          >
                            {feature}
                          </motion.span>
                        ))}
                      </motion.div>

                      {/* Magnetic CTA Link */}
                      <motion.div
                        className="mt-auto"
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.5 + index * 0.1 }}
                      >
                        <Link
                          to={service.link}
                          className="group/link inline-flex items-center text-accent hover:text-accent/80 font-semibold text-sm transition-all duration-300"
                        >
                          <motion.span
                            className="relative"
                            whileHover={{ x: 2 }}
                          >
                            Learn More
                            <motion.div
                              className="absolute bottom-0 left-0 h-0.5 bg-accent"
                              initial={{ width: 0 }}
                              whileHover={{ width: '100%' }}
                              transition={{ duration: 0.3 }}
                            />
                          </motion.span>
                          <ArrowRight className="w-4 h-4 ml-2 transition-transform group-hover/link:translate-x-1" />
                        </Link>
                      </motion.div>
                    </div>
                  </motion.div>
                </motion.div>
              </motion.div>
            );
          })}
        </div>

        {/* Enhanced CTA Section */}
        <motion.div
          className="text-center mt-16 md:mt-24"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={containerVariants}
        >
          <motion.div variants={fadeInUp}>
            <Button
              size="lg"
              className="bg-gradient-to-r from-accent to-accent/80 hover:from-accent/90 hover:to-accent/70 text-white px-8 py-4 text-lg font-bold rounded-full shadow-2xl hover:shadow-accent/25 transition-all duration-300"
            >
              Explore All Services
              <ArrowRight className="w-5 h-5 ml-2" />
            </Button>
          </motion.div>
          
          <motion.p
            className="text-sm md:text-base text-snow/50 mt-4 max-w-lg mx-auto"
            variants={fadeInUp}
          >
            Ready to transform your business with AI?
          </motion.p>
        </motion.div>
      </div>
    </section>
  );
};

export default Services;
