import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  <PERSON><PERSON><PERSON>, 
  ArrowRight, 
  Code2, 
  Database, 
  Cloud, 
  Server, 
  Cpu, 
  Network, 
  Zap, 
  FileCode, 
  Palette, 
  Layers, 
  LineChart, 
  MessageSquare, 
  FileJson, 
  HardDrive, 
  Search, 
  Container, 
  GitBranch,
  BrainCircuit
} from 'lucide-react';
import { Button } from './ui/button';

// Tech stack categories and their technologies
const techStack = {
  'Frontend': [
    {
      name: 'React',
      logo: '/tech/react.svg',
      description: 'A powerful JavaScript library for building interactive user interfaces with component-based architecture and virtual DOM for optimal performance.',
      icon: Code2
    },
    {
      name: 'TypeScript',
      logo: '/tech/typescript.svg',
      description: 'A strongly typed programming language that builds on JavaScript, adding static type definitions for better development experience and code reliability.',
      icon: FileCode
    },
    {
      name: 'Tailwind CSS',
      logo: '/tech/tailwind.svg',
      description: 'A utility-first CSS framework that enables rapid UI development with pre-built classes and customizable design system.',
      icon: Palette
    },
    {
      name: 'Next.js',
      logo: '/tech/nextjs.svg',
      description: 'A React framework that enables server-side rendering, static site generation, and optimized performance for modern web applications.',
      icon: Layers
    }
  ],
  'Backend': [
    {
      name: 'Node.js',
      logo: '/tech/nodejs.svg',
      description: 'A JavaScript runtime built on Chrome\'s V8 engine, enabling scalable server-side applications with non-blocking I/O operations.',
      icon: Server
    },
    {
      name: 'Python',
      logo: '/tech/python.svg',
      description: 'A versatile programming language known for its simplicity and readability, perfect for rapid development and data processing.',
      icon: FileCode
    },
    {
      name: 'FastAPI',
      logo: '/tech/fastapi.svg',
      description: 'A modern, fast web framework for building APIs with Python, featuring automatic documentation and high performance.',
      icon: Zap
    },
    {
      name: 'GraphQL',
      logo: '/tech/graphql.svg',
      description: 'A query language for APIs that enables clients to request exactly the data they need, reducing over-fetching and under-fetching.',
      icon: Network
    }
  ],
  'AI/ML': [
    {
      name: 'TensorFlow',
      logo: '/tech/tensorflow.svg',
      description: 'An open-source machine learning framework that enables building and training neural networks for various AI applications.',
      icon: BrainCircuit
    },
    {
      name: 'PyTorch',
      logo: '/tech/pytorch.svg',
      description: 'A deep learning framework that provides flexibility and speed for research and production deployment of AI models.',
      icon: Cpu
    },
    {
      name: 'Scikit-learn',
      logo: '/tech/scikit.svg',
      description: 'A comprehensive machine learning library for Python, offering tools for data mining, analysis, and model development.',
      icon: LineChart
    },
    {
      name: 'Hugging Face',
      logo: '/tech/huggingface.svg',
      description: 'A platform for state-of-the-art natural language processing models and tools for building AI applications.',
      icon: MessageSquare
    }
  ],
  'Database': [
    {
      name: 'PostgreSQL',
      logo: '/tech/postgresql.svg',
      description: 'A powerful, open-source object-relational database system with advanced features for data integrity and reliability.',
      icon: Database
    },
    {
      name: 'MongoDB',
      logo: '/tech/mongodb.svg',
      description: 'A flexible, document-oriented NoSQL database that scales horizontally and handles unstructured data efficiently.',
      icon: FileJson
    },
    {
      name: 'Redis',
      logo: '/tech/redis.svg',
      description: 'An in-memory data structure store used as a database, cache, message broker, and queue for high-performance applications.',
      icon: HardDrive
    },
    {
      name: 'Elasticsearch',
      logo: '/tech/elasticsearch.svg',
      description: 'A distributed search and analytics engine built on Apache Lucene, perfect for full-text search and real-time analytics.',
      icon: Search
    }
  ],
  'DevOps': [
    {
      name: 'Docker',
      logo: '/tech/docker.svg',
      description: 'A platform for developing, shipping, and running applications in containers, ensuring consistency across environments.',
      icon: Container
    },
    {
      name: 'Kubernetes',
      logo: '/tech/kubernetes.svg',
      description: 'An open-source container orchestration platform that automates deployment, scaling, and management of containerized applications.',
      icon: Network
    },
    {
      name: 'AWS',
      logo: '/tech/aws.svg',
      description: 'A comprehensive cloud computing platform offering over 200 fully featured services for building scalable applications.',
      icon: Cloud
    },
    {
      name: 'GitHub Actions',
      logo: '/tech/github.svg',
      description: 'An automation platform that enables CI/CD workflows directly in GitHub repositories for seamless development processes.',
      icon: GitBranch
    }
  ],
};

const categoryIcons = {
  'Frontend': <Code2 className="w-5 h-5" />,
  'Backend': <Server className="w-5 h-5" />,
  'AI/ML': <Cpu className="w-5 h-5" />,
  'Database': <Database className="w-5 h-5" />,
  'DevOps': <Cloud className="w-5 h-5" />,
};

const TechStack = () => {
  const [selectedCategory, setSelectedCategory] = useState('Frontend');
  const [hoveredTech, setHoveredTech] = useState<string | null>(null);

  const fadeInUp = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.8,
        ease: [0.22, 1, 0.36, 1]
      }
    }
  };

  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  return (
    <section className="py-24 px-4 relative overflow-hidden">
      {/* Animated background decoration */}
      <motion.div 
        className="absolute inset-0 opacity-30"
        animate={{
          background: [
            "radial-gradient(circle at 20% 20%, rgba(16, 185, 129, 0.1) 0%, transparent 50%)",
            "radial-gradient(circle at 80% 80%, rgba(16, 185, 129, 0.1) 0%, transparent 50%)",
            "radial-gradient(circle at 20% 20%, rgba(16, 185, 129, 0.1) 0%, transparent 50%)",
          ]
        }}
        transition={{ duration: 10, repeat: Infinity }}
      />

      <div className="max-w-7xl mx-auto relative">
        <motion.div
          className="text-center mb-12 sm:mb-16"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={fadeInUp}
        >
          <motion.div 
            className="inline-flex items-center gap-2 px-3 sm:px-4 py-1.5 sm:py-2 rounded-full bg-emerald-500/10 border border-emerald-500/20 mb-4 sm:mb-6"
            variants={fadeInUp}
          >
            <Sparkles className="w-3.5 h-3.5 sm:w-4 sm:h-4 text-emerald-400" />
            <span className="text-xs sm:text-sm font-medium text-emerald-400">Our Technology</span>
          </motion.div>

          <motion.h2 
            className="text-3xl sm:text-4xl md:text-5xl font-bold mb-4 sm:mb-6 bg-clip-text text-transparent bg-gradient-to-r from-emerald-400 via-cyan-400 to-emerald-400"
            variants={fadeInUp}
          >
            Technology Stack
          </motion.h2>

          <motion.p 
            className="text-base sm:text-lg md:text-xl leading-relaxed text-snow/90 max-w-3xl mx-auto px-4 sm:px-0"
            variants={fadeInUp}
          >
            Built with cutting-edge technologies to deliver powerful, scalable, and innovative solutions.
          </motion.p>
        </motion.div>

        {/* Category Selector */}
        <motion.div 
          className="flex flex-wrap justify-center gap-2 mb-8 px-4 sm:px-0"
          variants={staggerContainer}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          {Object.keys(techStack).map((category) => (
            <motion.button
              key={category}
              onClick={() => setSelectedCategory(category)}
              className={`flex items-center gap-2 px-4 py-2 rounded-full transition-all duration-300 ${
                selectedCategory === category
                  ? 'bg-emerald-500 text-black'
                  : 'bg-white/5 text-snow/70 hover:bg-white/10'
              }`}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              variants={fadeInUp}
            >
              {categoryIcons[category]}
              <span className="text-sm font-medium">{category}</span>
            </motion.button>
          ))}
        </motion.div>

        {/* Tech Grid */}
        <motion.div 
          className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 px-4 sm:px-0"
          variants={staggerContainer}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          <AnimatePresence mode="wait">
            {techStack[selectedCategory].map((tech) => (
              <motion.div
                key={tech.name}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
                transition={{ duration: 0.3 }}
                className="group relative p-6 rounded-2xl bg-white/5 backdrop-blur-sm border border-white/10 hover:border-emerald-500/20 transition-all duration-300"
                onHoverStart={() => setHoveredTech(tech.name)}
                onHoverEnd={() => setHoveredTech(null)}
                whileHover={{ y: -5 }}
              >
                <div className="flex items-center gap-4 mb-4">
                  <div className="w-12 h-12 rounded-xl bg-white/10 flex items-center justify-center">
                    <img 
                      src={tech.logo} 
                      alt={`${tech.name} logo`} 
                      className="w-8 h-8 object-contain"
                    />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-white">{tech.name}</h3>
                    <p className="text-sm text-snow/70">{tech.description}</p>
                  </div>
                </div>

                <motion.div
                  className="absolute inset-0 rounded-2xl bg-gradient-to-r from-emerald-500/10 to-cyan-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                  initial={false}
                  animate={{
                    scale: hoveredTech === tech.name ? 1.05 : 1,
                  }}
                />
              </motion.div>
            ))}
          </AnimatePresence>
        </motion.div>

        {/* Integration Section */}
        <motion.div
          className="mt-16 text-center"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={fadeInUp}
        >
          <h3 className="text-2xl sm:text-3xl font-bold mb-6 text-white">
            Seamless Integration with Popular Platforms
          </h3>
          
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4 sm:gap-6 px-4 sm:px-0">
            {['Slack', 'Discord', 'GitHub', 'GitLab', 'Jira', 'Notion'].map((platform) => (
              <motion.div
                key={platform}
                className="p-4 rounded-xl bg-white/5 backdrop-blur-sm border border-white/10 hover:border-emerald-500/20 transition-all duration-300"
                whileHover={{ y: -5, scale: 1.05 }}
              >
                <div className="flex flex-col items-center gap-2">
                  <Network className="w-6 h-6 text-emerald-400" />
                  <span className="text-sm font-medium text-white">{platform}</span>
                </div>
              </motion.div>
            ))}
          </div>

          <motion.div
            className="mt-12"
            variants={fadeInUp}
          >
            <Button
              size="lg"
              className="w-full sm:w-auto bg-gradient-to-r from-emerald-400 to-cyan-400 text-black hover:opacity-90 transition-all duration-300 group"
            >
              Explore Integrations
              <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
            </Button>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default TechStack; 