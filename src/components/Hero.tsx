import React, { Suspense, useRef, useEffect, useState } from 'react';
import { motion, useReducedMotion, useScroll, useTransform, useSpring, useMotionValue, useVelocity } from 'framer-motion';
import { Link } from 'react-router-dom';
import { ArrowRight, Brain, Lightbulb, Zap, Wrench, GraduationCap, Sparkles, Users, Atom, Orbit, Eye, Star, Layers } from 'lucide-react';
import { Button } from './ui/button';

const NetworkAnimation = React.lazy(() => import('./NetworkAnimation'));

const Hero = () => {
  const shouldReduceMotion = useReducedMotion();
  const { scrollY } = useScroll();
  const y = useTransform(scrollY, [0, 500], [0, 100]);
  const opacity = useTransform(scrollY, [0, 300], [1, 0]);

  // Revolutionary state management
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isHovering, setIsHovering] = useState(false);
  const containerRef = useRef(null);

  // Advanced motion values for immersive effects
  const mouseX = useMotionValue(0);
  const mouseY = useMotionValue(0);
  const mouseXSpring = useSpring(mouseX, { stiffness: 500, damping: 100 });
  const mouseYSpring = useSpring(mouseY, { stiffness: 500, damping: 100 });

  // Parallax transforms for depth
  const backgroundY = useTransform(scrollY, [0, 1000], [0, -300]);
  const midgroundY = useTransform(scrollY, [0, 1000], [0, -150]);
  const foregroundY = useTransform(scrollY, [0, 1000], [0, -50]);

  // Mouse tracking for magnetic effects
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      const { clientX, clientY } = e;
      setMousePosition({ x: clientX, y: clientY });
      mouseX.set(clientX);
      mouseY.set(clientY);
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, [mouseX, mouseY]);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 30, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: [0.22, 1, 0.36, 1]
      }
    }
  };

  return (
    <section
      ref={containerRef}
      className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-midnight via-midnight to-darkShade perspective-1000"
    >
      {/* Revolutionary 3D Background Layers */}
      <motion.div
        className="absolute inset-0 opacity-30"
        style={{ y: backgroundY }}
      >
        <Suspense fallback={<div className="w-full h-full bg-midnight" />}>
          <NetworkAnimation />
        </Suspense>
      </motion.div>

      {/* Immersive Particle System */}
      <motion.div
        className="absolute inset-0 pointer-events-none"
        style={{ y: midgroundY }}
      >
        {[...Array(20)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              x: useTransform(mouseXSpring, (x) => (x - (typeof window !== 'undefined' ? window.innerWidth : 1920) / 2) * (0.02 + Math.random() * 0.02)),
              y: useTransform(mouseYSpring, (y) => (y - (typeof window !== 'undefined' ? window.innerHeight : 1080) / 2) * (0.02 + Math.random() * 0.02)),
            }}
            animate={{
              y: [0, -30, 0],
              opacity: [0.2, 0.8, 0.2],
              scale: [1, 1.5, 1],
            }}
            transition={{
              duration: 4 + Math.random() * 3,
              repeat: Infinity,
              delay: Math.random() * 2,
              ease: "easeInOut",
            }}
          >
            {i % 4 === 0 && <Atom className="w-3 h-3 text-accent/40" />}
            {i % 4 === 1 && <Orbit className="w-2 h-2 text-accent/30" />}
            {i % 4 === 2 && <Star className="w-2 h-2 text-accent/50" />}
            {i % 4 === 3 && <div className="w-1 h-1 bg-accent/40 rounded-full" />}
          </motion.div>
        ))}
      </motion.div>

      {/* Dynamic Gradient Overlay with Depth */}
      <motion.div
        className="absolute inset-0 bg-gradient-to-b from-transparent via-midnight/10 to-midnight/40"
        style={{ y: foregroundY }}
      />

      {/* Floating Geometric Elements */}
      <motion.div
        className="absolute top-20 right-20 w-32 h-32 pointer-events-none opacity-20"
        style={{
          x: useTransform(mouseXSpring, (x) => (x - (typeof window !== 'undefined' ? window.innerWidth : 1920) / 2) * 0.05),
          y: useTransform(mouseYSpring, (y) => (y - (typeof window !== 'undefined' ? window.innerHeight : 1080) / 2) * 0.05),
        }}
        animate={{
          rotate: 360,
          scale: [1, 1.1, 1],
        }}
        transition={{
          rotate: { duration: 30, repeat: Infinity, ease: "linear" },
          scale: { duration: 6, repeat: Infinity, ease: "easeInOut" },
        }}
      >
        <div className="w-full h-full border-2 border-accent/30 rounded-full" />
        <div className="absolute inset-8 border border-accent/20 rounded-full" />
        <div className="absolute inset-16 bg-accent/10 rounded-full" />
      </motion.div>

      <motion.div
        className="absolute bottom-32 left-20 w-24 h-24 pointer-events-none opacity-15"
        style={{
          x: useTransform(mouseXSpring, (x) => (x - (typeof window !== 'undefined' ? window.innerWidth : 1920) / 2) * -0.03),
          y: useTransform(mouseYSpring, (y) => (y - (typeof window !== 'undefined' ? window.innerHeight : 1080) / 2) * -0.03),
        }}
        animate={{
          rotate: [0, 45, 0],
          scale: [1, 1.3, 1],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      >
        <div className="w-full h-full bg-gradient-to-br from-accent/20 to-transparent rounded-lg transform rotate-45" />
      </motion.div>
      
      <motion.div 
        className="relative z-10 px-4 sm:px-6 lg:px-8 max-w-6xl mx-auto text-center"
        initial="hidden"
        animate="visible"
        variants={containerVariants}
        style={{ y, opacity }}
      >
        {/* Revolutionary Impact Badge with Morphing Effects */}
        <motion.div
          className="inline-flex items-center gap-3 px-4 sm:px-6 py-3 rounded-full bg-gradient-to-r from-accent/20 via-accent/10 to-accent/20 border border-accent/30 mb-8 sm:mb-12 backdrop-blur-md shadow-2xl shadow-accent/10"
          variants={itemVariants}
          whileHover={{
            scale: 1.05,
            boxShadow: "0 0 30px rgba(56, 173, 169, 0.3)",
            borderColor: "rgba(56, 173, 169, 0.5)",
          }}
          onHoverStart={() => setIsHovering(true)}
          onHoverEnd={() => setIsHovering(false)}
        >
          <motion.div
            className="relative"
            animate={{ rotate: 360 }}
            transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
          >
            <motion.div
              className="w-6 h-6 rounded-full bg-gradient-to-r from-accent to-accent/60 flex items-center justify-center"
              animate={{
                scale: isHovering ? [1, 1.2, 1] : 1,
                boxShadow: isHovering
                  ? ["0 0 0px rgba(56, 173, 169, 0.4)", "0 0 20px rgba(56, 173, 169, 0.8)", "0 0 0px rgba(56, 173, 169, 0.4)"]
                  : "0 0 0px rgba(56, 173, 169, 0.4)",
              }}
              transition={{ duration: 2, repeat: Infinity }}
            >
              <Brain className="w-3 h-3 text-white" />
            </motion.div>
            <motion.div
              className="absolute inset-0 rounded-full border-2 border-accent/30"
              animate={{
                scale: [1, 1.5, 1],
                opacity: [0.5, 0, 0.5],
              }}
              transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
            />
          </motion.div>
          <motion.span
            className="text-sm sm:text-base font-semibold bg-gradient-to-r from-accent via-snow to-accent bg-clip-text text-transparent"
            animate={{
              backgroundPosition: ["0% 50%", "100% 50%", "0% 50%"],
            }}
            transition={{ duration: 5, repeat: Infinity, ease: "linear" }}
            style={{ backgroundSize: "200% 200%" }}
          >
            The Sainpse Institute of Augmented Intelligence
          </motion.span>
        </motion.div>
        
        {/* Revolutionary 3D Main Headline */}
        <motion.div className="relative mb-6 sm:mb-8">
          <motion.h1
            className="text-4xl sm:text-5xl md:text-7xl lg:text-8xl font-bold leading-tight px-2 relative z-10"
            variants={itemVariants}
            style={{
              x: useTransform(mouseXSpring, (x) => (x - (typeof window !== 'undefined' ? window.innerWidth : 1920) / 2) * 0.01),
              y: useTransform(mouseYSpring, (y) => (y - (typeof window !== 'undefined' ? window.innerHeight : 1080) / 2) * 0.01),
            }}
          >
            <motion.span
              className="block text-snow mb-2 sm:mb-4"
              whileHover={{
                textShadow: "0 0 20px rgba(255, 255, 255, 0.5)",
                scale: 1.02,
              }}
              transition={{ duration: 0.3 }}
            >
              The Future is
            </motion.span>
            <motion.span
              className="block bg-gradient-to-r from-accent via-accent/80 to-accent bg-clip-text text-transparent relative"
              animate={{
                backgroundPosition: ["0% 50%", "100% 50%", "0% 50%"],
              }}
              transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
              style={{ backgroundSize: "200% 200%" }}
              whileHover={{
                scale: 1.02,
                textShadow: "0 0 30px rgba(56, 173, 169, 0.6)",
              }}
            >
              Collaborative Intelligence
              <motion.div
                className="absolute inset-0 bg-gradient-to-r from-accent/20 via-transparent to-accent/20 blur-xl"
                animate={{
                  opacity: [0, 0.5, 0],
                  scale: [0.8, 1.2, 0.8],
                }}
                transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
              />
            </motion.span>
          </motion.h1>

          {/* 3D Shadow Effect */}
          <motion.h1
            className="absolute top-2 left-2 text-4xl sm:text-5xl md:text-7xl lg:text-8xl font-bold leading-tight px-2 text-accent/10 -z-10"
            style={{
              x: useTransform(mouseXSpring, (x) => (x - (typeof window !== 'undefined' ? window.innerWidth : 1920) / 2) * -0.005),
              y: useTransform(mouseYSpring, (y) => (y - (typeof window !== 'undefined' ? window.innerHeight : 1080) / 2) * -0.005),
            }}
          >
            <span className="block mb-2 sm:mb-4">The Future is</span>
            <span className="block">Collaborative Intelligence</span>
          </motion.h1>
        </motion.div>
        
        {/* Powerful Subheadline */}
        <motion.p 
          className="text-base sm:text-lg md:text-xl lg:text-2xl mb-6 sm:mb-8 text-snow/90 max-w-4xl mx-auto leading-relaxed font-medium px-2"
          variants={itemVariants}
        >
          Join a global network of visionaries who are 
          <span className="text-accent font-bold"> redefining what's possible</span> when human creativity and AI unite.
        </motion.p>

        {/* Urgency Statement */}
        <motion.div 
          className="mb-8 sm:mb-12 max-w-3xl mx-auto px-2"
          variants={itemVariants}
        >
          <p className="text-sm sm:text-base md:text-lg text-snow/70 leading-relaxed">
            The revolution has begun. Companies embracing augmented intelligence are 
            <span className="text-accent font-semibold"> 3x more innovative</span> and 
            <span className="text-accent font-semibold"> 5x faster to market</span>. 
            Don't get left behind.
          </p>
        </motion.div>

        {/* Enhanced CTAs with Urgency */}
        <motion.div 
          className="flex flex-col sm:flex-row gap-4 justify-center mb-12 sm:mb-16"
          variants={itemVariants}
        >
          <Link to="/tools" className="w-full sm:w-auto">
            <Button
              size="lg"
              className="w-full sm:w-auto bg-gradient-to-r from-accent to-accent/80 hover:from-accent/90 hover:to-accent/70 text-white px-8 sm:px-10 py-4 sm:py-5 text-base sm:text-lg font-bold rounded-full shadow-2xl hover:shadow-accent/25 transition-all duration-300 group relative overflow-hidden"
            >
              <motion.div
                className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent"
                initial={{ x: "-100%" }}
                whileHover={{ x: "100%" }}
                transition={{ duration: 0.6 }}
              />
              <Wrench className="mr-2 sm:mr-3 h-5 w-5 sm:h-6 sm:w-6 transition-transform group-hover:rotate-12" />
              Start Building Now
              <ArrowRight className="ml-2 sm:ml-3 h-5 w-5 sm:h-6 sm:w-6 transition-transform group-hover:translate-x-1" />
            </Button>
          </Link>
          <Link to="/education" className="w-full sm:w-auto">
            <Button
              size="lg"
              variant="outline"
              className="w-full sm:w-auto border-2 border-accent/50 text-accent hover:bg-accent hover:text-white px-8 sm:px-10 py-4 sm:py-5 text-base sm:text-lg font-bold rounded-full shadow-xl hover:shadow-2xl transition-all duration-300 group backdrop-blur-sm"
            >
              <GraduationCap className="mr-2 sm:mr-3 h-5 w-5 sm:h-6 sm:w-6 transition-transform group-hover:-rotate-12" />
              Master AI Today
              <ArrowRight className="ml-2 sm:ml-3 h-5 w-5 sm:h-6 sm:w-6 transition-transform group-hover:translate-x-1" />
            </Button>
          </Link>
        </motion.div>

        {/* Compelling Scroll Indicator */}
        <motion.div
          className="flex flex-col items-center gap-2 mt-8 sm:mt-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.5 }}
          variants={itemVariants}
        >
          <motion.div
            animate={{ y: [0, 8, 0] }}
            transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
            className="flex flex-col items-center gap-2"
          >
            <span className="text-xs text-accent font-semibold uppercase tracking-wide">
              Discover Your Potential
            </span>
            <div className="w-6 h-10 rounded-full border-2 border-accent/40 flex justify-center p-1 bg-accent/5">
              <motion.div
                className="w-1 h-2 rounded-full bg-accent"
                animate={{ y: [0, 16, 0] }}
                transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
              />
            </div>
          </motion.div>
        </motion.div>
      </motion.div>
    </section>
  );
};

export default Hero;
