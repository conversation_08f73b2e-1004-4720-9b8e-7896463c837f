import React from 'react';
import { motion } from 'framer-motion';
import { Github, Twitter, Linkedin, Mail, MapPin, Phone } from 'lucide-react';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: [0.22, 1, 0.36, 1]
      }
    }
  };

  const footerLinks = {
    Product: [
      { label: 'Features', href: '#features' },
      { label: 'Pricing', href: '#pricing' },
      { label: 'Security', href: '#security' },
      { label: 'Integrations', href: '#integrations' }
    ],
    Company: [
      { label: 'About Us', href: '#mission' },
      { label: 'Careers', href: '#careers' },
      { label: 'Blog', href: '#blog' },
      { label: 'Press', href: '#press' }
    ],
    Support: [
      { label: 'Help Center', href: '#help' },
      { label: 'Documentation', href: '#docs' },
      { label: 'API Reference', href: '#api' },
      { label: 'Status', href: '#status' }
    ]
  };

  const socialLinks = [
    { icon: <Github className="w-5 h-5" />, href: 'https://github.com/Sainpse', label: 'GitHub' },
    { icon: <Twitter className="w-5 h-5" />, href: 'https://x.com/Sainpse', label: 'Twitter' },
    { icon: <Linkedin className="w-5 h-5" />, href: 'https://www.linkedin.com/company/sainpse', label: 'LinkedIn' },
    { icon: <Mail className="w-5 h-5" />, href: 'mailto:<EMAIL>', label: 'Email' }
  ];

  return (
    <footer className="bg-darkShade border-t border-white/10">
      <div className="container mx-auto px-6 lg:px-8">
        {/* Main Footer Content */}
        <motion.div 
          className="py-16"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={containerVariants}
        >
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12">
            {/* Brand Section */}
            <motion.div className="lg:col-span-1" variants={itemVariants}>
              <div className="flex items-center mb-6">
                <img src="/latest_logo2_black_NObg.png" alt="Sainpse Logo" className="w-8 h-8 mr-3" />
                <h3 className="text-xl font-bold text-snow">Sainpse</h3>
              </div>
              <p className="text-snow/70 mb-6 max-w-sm leading-relaxed">
                Empowering teams with intelligent collaboration tools that transform how you work, create, and innovate together.
              </p>
              
              {/* Contact Info */}
              <div className="space-y-3">
                <div className="flex items-center text-sm text-snow/60">
                  <Mail className="w-4 h-4 mr-3 text-accent" />
                  <span><EMAIL></span>
                </div>
                <div className="flex items-center text-sm text-snow/60">
                  <Phone className="w-4 h-4 mr-3 text-accent" />
                  <span>+27 (74) 944-2626</span>
                </div>
                <div className="flex items-center text-sm text-snow/60">
                  <MapPin className="w-4 h-4 mr-3 text-accent" />
                  <span>Global • Remote First</span>
                </div>
              </div>
            </motion.div>

            {/* Links Sections */}
            {Object.entries(footerLinks).map(([category, links]) => (
              <motion.div key={category} variants={itemVariants}>
                <h4 className="text-snow font-semibold mb-6">{category}</h4>
                <ul className="space-y-3">
                  {links.map((link, index) => (
                    <li key={index}>
                      <a 
                        href={link.href}
                        className="text-snow/60 hover:text-accent transition-colors duration-200 text-sm"
                      >
                        {link.label}
                      </a>
                    </li>
                  ))}
                </ul>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Bottom Section */}
        <motion.div 
          className="py-8 border-t border-white/10"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={containerVariants}
        >
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            {/* Copyright */}
            <motion.p 
              className="text-snow/50 text-sm"
              variants={itemVariants}
            >
              © {currentYear} Sainpse Institute. All rights reserved.
            </motion.p>

            {/* Social Links */}
            <motion.div 
              className="flex items-center space-x-6"
              variants={itemVariants}
            >
              {socialLinks.map((social, index) => (
                <a
                  key={index}
                  href={social.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-snow/60 hover:text-accent transition-colors duration-200"
                  aria-label={social.label}
                >
                  {social.icon}
                </a>
              ))}
            </motion.div>

            {/* Legal Links */}
            <motion.div 
              className="flex items-center space-x-6 text-sm"
              variants={itemVariants}
            >
              <a href="#privacy" className="text-snow/50 hover:text-accent transition-colors duration-200">
                Privacy Policy
              </a>
              <a href="#terms" className="text-snow/50 hover:text-accent transition-colors duration-200">
                Terms of Service
              </a>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </footer>
  );
};

export default Footer;