import { useState } from 'react';
import { createClient } from '@supabase/supabase-js';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Eye, EyeOff } from 'lucide-react';
import { useToast } from './ui/use-toast';

const supabase = createClient(
  import.meta.env.VITE_SUPABASE_URL,
  import.meta.env.VITE_SUPABASE_ANON_KEY
);

export const Auth = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [mode, setMode] = useState('login'); // 'login' | 'signup' | 'reset' | 'update-password'
  const [newPassword, setNewPassword] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [message, setMessage] = useState<string | null>(null);
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const validateEmail = (email: string) => {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  };

  const validateName = (name: string) => {
    return name.length >= 2 && /^[a-zA-Z]+$/.test(name);
  };

  const validatePassword = (password: string) => {
    return password.length >= 8 && 
           /[A-Z]/.test(password) &&
           /[0-9]/.test(password) &&
           /[!@#$%^&*]/.test(password);
  };

  const [confirmPassword, setConfirmPassword] = useState('');
  const [lastRequestTime, setLastRequestTime] = useState(0);

  const checkRateLimit = () => {
    const now = Date.now();
    if (now - lastRequestTime < 5000) { // 5 second rate limit
      setError('Please wait a few seconds before trying again');
      return false;
    }
    setLastRequestTime(now);
    return true;
  };

  const handleEmailLogin = async () => {
    if (!checkRateLimit()) return;
    if (!validateEmail(email)) {
      setError('Please enter a valid email address (e.g., <EMAIL>)');
      return;
    }
    if (!validatePassword(password)) {
      setError('Password must be at least 8 characters with one uppercase, one number, and one special character');
      return;
    }

    setError(null);
    setMessage(null);
    setIsLoading(true);
    try {
      const { data, error: authError } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (authError) throw authError;
      toast({
        title: 'Login successful',
        description: 'Redirecting...',
      });
      // TODO: Redirect or update app state
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Login failed';
      setError(message);
      toast({
        title: 'Login failed',
        description: message,
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSignup = async () => {
    if (!checkRateLimit()) return;
    if (!validatePassword(password)) {
      setError('Password must be at least 8 characters with one uppercase, one number, and one special character');
      return;
    }
    if (password !== confirmPassword) {
      setError('Passwords do not match');
      return;
    }
    
    setError(null);
    setMessage(null);
    setIsLoading(true);
    try {
      const { data: { user }, error: authError } = await supabase.auth.signUp({
        email,
        password,
        options: {
          emailRedirectTo: window.location.origin,
          data: {
            first_name: firstName,
            last_name: lastName
          }
        },
      });

      if (authError) throw authError;
      
      // Update user profile
      if (user) {
        await supabase
          .from('profiles')
          .upsert({
            id: user.id,
            first_name: firstName,
            last_name: lastName,
            updated_at: new Date().toISOString(),
          });
      }

      setMessage('Check your email for the confirmation link!');
      toast({
        title: 'Signup successful',
        description: 'Please check your email to confirm your account',
      });
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Signup failed';
      setError(message);
      toast({
        title: 'Signup failed',
        description: message,
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handlePasswordUpdate = async () => {
    if (!validatePassword(newPassword)) {
      setError('Password must be at least 8 characters with one uppercase, one number, and one special character');
      return;
    }
    if (newPassword !== confirmPassword) {
      setError('Passwords do not match');
      return;
    }

    setError(null);
    setMessage(null);
    setIsLoading(true);
    try {
      const { data, error: authError } = await supabase.auth.updateUser({
        password: newPassword,
      });

      if (authError) throw authError;
      
      toast({
        title: 'Password updated',
        description: 'Your password has been successfully updated',
      });
      setMode('login');
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Password update failed';
      setError(message);
      toast({
        title: 'Password update failed',
        description: message,
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handlePasswordReset = async () => {
    if (!checkRateLimit()) return;
    if (!validateEmail(email)) {
      setError('Please enter a valid email address');
      return;
    }

    setError(null);
    setMessage(null);
    setIsLoading(true);
    try {
      const { data, error: authError } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/update-password`,
      });

      if (authError) throw authError;
      
      toast({
        title: 'Reset email sent',
        description: 'Check your email for the password reset link',
      });
      setMode('login');
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Password reset failed';
      setError(message);
      toast({
        title: 'Password reset failed',
        description: message,
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="max-w-md mx-auto p-6 bg-white rounded-lg shadow-md">
      {error && <div className="mb-4 text-red-500">{error}</div>}
      {message && <div className="mb-4 text-green-500">{message}</div>}

      {mode === 'login' && (
        <>
          <h2 className="text-2xl font-bold mb-6">Login</h2>
          <div className="space-y-4">
            <div>
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="password">Password</Label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? 'text' : 'password'}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                />
                <button
                  type="button"
                  className="absolute right-2 top-2.5 text-gray-500 hover:text-gray-700"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                </button>
              </div>
            </div>
            <Button onClick={handleEmailLogin} className="w-full" disabled={isLoading}>
              {isLoading ? 'Logging in...' : 'Login'}
            </Button>
            <div className="text-center">
              <Button variant="link" onClick={() => setMode('signup')}>
                Create account
              </Button>
              <Button variant="link" onClick={() => setMode('reset')}>
                Forgot password?
              </Button>
            </div>
          </div>
        </>
      )}

      {mode === 'signup' && (
        <>
          <h2 className="text-2xl font-bold mb-6">Sign Up</h2>
          <div className="space-y-4">
            <div>
              <Label htmlFor="firstName">First Name</Label>
              <Input
                id="firstName"
                type="text"
                value={firstName}
                onChange={(e) => setFirstName(e.target.value)}
                required
              />
            </div>
            <div>
              <Label htmlFor="lastName">Last Name</Label>
              <Input
                id="lastName"
                type="text"
                value={lastName}
                onChange={(e) => setLastName(e.target.value)}
                required
              />
            </div>
            <div>
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
              />
            </div>
            <div>
              <Label htmlFor="password">Password</Label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? 'text' : 'password'}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  minLength={8}
                />
                <button
                  type="button"
                  className="absolute right-2 top-2.5 text-gray-500 hover:text-gray-700"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                </button>
              </div>
              <p className="text-sm text-gray-500 mt-1">
                Password must be at least 8 characters with one uppercase, one number, and one special character
              </p>
            </div>
            <div>
              <Label htmlFor="confirmPassword">Confirm Password</Label>
              <div className="relative">
                <Input
                  id="confirmPassword"
                  type={showPassword ? 'text' : 'password'}
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  required
                  minLength={8}
                />
                <button
                  type="button"
                  className="absolute right-2 top-2.5 text-gray-500 hover:text-gray-700"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                </button>
              </div>
            </div>
            <Button 
              onClick={handleSignup} 
              className="w-full"
              disabled={isLoading}
            >
              {isLoading ? 'Creating account...' : 'Sign Up'}
            </Button>
            <div className="text-center">
              <Button variant="link" onClick={() => setMode('login')}>
                Already have an account? Login
              </Button>
            </div>
          </div>
        </>
      )}

      {mode === 'update-password' && (
        <>
          <h2 className="text-2xl font-bold mb-6">Update Password</h2>
          <div className="space-y-4">
            <div>
              <Label htmlFor="newPassword">New Password</Label>
              <div className="relative">
                <Input
                  id="newPassword"
                  type={showPassword ? 'text' : 'password'}
                  value={newPassword}
                  onChange={(e) => setNewPassword(e.target.value)}
                  required
                  minLength={8}
                />
                <button
                  type="button"
                  className="absolute right-2 top-2.5 text-gray-500 hover:text-gray-700"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                </button>
              </div>
              <p className="text-sm text-gray-500 mt-1">
                Password must be at least 8 characters with one uppercase, one number, and one special character
              </p>
            </div>
            <div>
              <Label htmlFor="confirmNewPassword">Confirm New Password</Label>
              <div className="relative">
                <Input
                  id="confirmNewPassword"
                  type={showPassword ? 'text' : 'password'}
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  required
                  minLength={8}
                />
                <button
                  type="button"
                  className="absolute right-2 top-2.5 text-gray-500 hover:text-gray-700"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                </button>
              </div>
            </div>
            <Button 
              onClick={handlePasswordUpdate} 
              className="w-full"
              disabled={isLoading}
            >
              {isLoading ? 'Updating password...' : 'Update Password'}
            </Button>
          </div>
        </>
      )}

      {mode === 'reset' && (
        <>
          <h2 className="text-2xl font-bold mb-6">Reset Password</h2>
          <div className="space-y-4">
            <div>
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
              />
            </div>
            <Button 
              onClick={handlePasswordReset} 
              className="w-full"
              disabled={isLoading}
            >
              {isLoading ? 'Sending reset email...' : 'Reset Password'}
            </Button>
            <div className="text-center">
              <Button variant="link" onClick={() => setMode('login')}>
                Back to Login
              </Button>
            </div>
          </div>
        </>
      )}
    </div>
  );
};
