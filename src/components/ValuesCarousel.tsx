import React from 'react';
import { Brain, Lightbulb, Minimize2, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, ArrowRight } from 'lucide-react';
import { motion } from 'framer-motion';
import { Button } from './ui/button';

const values = [
  { 
    icon: Users, 
    title: 'Collaboration', 
    description: 'We believe in the power of collective effort, where individuals, businesses, and communities work together in harmony to achieve extraordinary outcomes and unlock emergent intelligence.',
    color: 'from-accent/20 to-accent/5',
    borderColor: 'border-accent/30',
    highlight: 'Collective Intelligence'
  },
  { 
    icon: Lightbulb, 
    title: 'Innovation', 
    description: "We are committed to fostering groundbreaking ideas, leveraging cutting-edge technologies, and pushing the boundaries of what's possible to drive transformative progress.",
    color: 'from-accent/20 to-accent/5',
    borderColor: 'border-accent/30',
    highlight: 'Boundary Breaking'
  },
  { 
    icon: Minimize2, 
    title: 'Simplicity', 
    description: 'We value clarity and ease, striving to simplify complex systems and solutions to make intelligence and progress accessible to all.',
    color: 'from-accent/20 to-accent/5',
    borderColor: 'border-accent/30',
    highlight: 'Elegant Solutions'
  },
  { 
    icon: Zap, 
    title: 'Empowerment', 
    description: 'We strive to enable individuals, organizations, and communities to realize their full potential by providing tools, knowledge, and opportunities to create lasting impact.',
    color: 'from-accent/20 to-accent/5',
    borderColor: 'border-accent/30',
    highlight: 'Unleashing Potential'
  },
  { 
    icon: Brain, 
    title: 'Emergence', 
    description: 'We embrace the philosophy that intelligence arises not only through advanced technologies but through systems, networks, and relationships working in unity to achieve more together.',
    color: 'from-accent/20 to-accent/5',
    borderColor: 'border-accent/30',
    highlight: 'Synergistic Growth'
  }
];

const ValuesCarousel = () => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 30, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: [0.22, 1, 0.36, 1]
      }
    }
  };

  const cardVariants = {
    hidden: { y: 20, opacity: 0, scale: 0.95 },
    visible: (i: number) => ({
      y: 0,
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.5,
        ease: [0.22, 1, 0.36, 1],
        delay: i * 0.1
      }
    })
  };

  return (
    <section className="py-24 bg-gradient-to-b from-darkShade to-midnight">
      <div className="container mx-auto px-6 lg:px-8">
        {/* Header */}
        <motion.div
          className="text-center mb-16"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={containerVariants}
        >
          <motion.div 
            className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-accent/10 border border-accent/20 mb-6"
            variants={itemVariants}
          >
            <Sparkles className="w-4 h-4 text-accent" />
            <span className="text-sm font-medium text-accent">Our Core Values</span>
          </motion.div>

          <motion.h2 
            className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4 text-snow"
            variants={itemVariants}
          >
            What Drives Us
          </motion.h2>

          <motion.p 
            className="text-lg md:text-xl text-snow/70 max-w-3xl mx-auto"
            variants={itemVariants}
          >
            Our values are the foundation of everything we do, guiding our mission to transform the future of collaboration
          </motion.p>
        </motion.div>

        {/* Values Grid */}
        <motion.div 
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={containerVariants}
        >
          {values.map((value, index) => (
            <motion.div
              key={index}
              className={`group relative p-8 rounded-3xl bg-white/5 backdrop-blur-sm border border-white/10 hover:border-accent/30 transition-all duration-500 overflow-hidden`}
              variants={cardVariants}
              custom={index}
              whileHover={{ 
                y: -8, 
                scale: 1.02,
                transition: { duration: 0.3 }
              }}
            >
              {/* Background gradient on hover */}
              <div className={`absolute inset-0 bg-gradient-to-br ${value.color} opacity-0 group-hover:opacity-100 transition-opacity duration-500`} />
              
              {/* Content */}
              <div className="relative z-10">
                {/* Icon */}
                <motion.div
                  className="text-accent mb-6 group-hover:scale-110 group-hover:text-accent transition-all duration-300"
                  whileHover={{ rotate: 5 }}
                >
                  {React.createElement(value.icon, {
                    className: "w-8 h-8"
                  })}
                </motion.div>

                {/* Title */}
                <h3 className="text-xl font-semibold text-snow mb-2 group-hover:text-accent transition-colors duration-300">
                  {value.title}
                </h3>

                {/* Highlight */}
                <p className="text-sm font-medium text-accent/80 mb-4 group-hover:text-accent transition-colors duration-300">
                  {value.highlight}
                </p>

                {/* Description */}
                <p className="text-snow/70 leading-relaxed text-sm group-hover:text-snow/90 transition-colors duration-300">
                  {value.description}
                </p>
              </div>

              {/* Decorative elements */}
              <div className="absolute top-4 right-4 w-20 h-20 rounded-full bg-accent/5 -translate-y-10 translate-x-10 group-hover:translate-x-6 group-hover:-translate-y-6 transition-transform duration-700" />
              <div className="absolute bottom-4 left-4 w-16 h-16 rounded-full bg-accent/10 translate-y-8 -translate-x-8 group-hover:-translate-x-4 group-hover:translate-y-4 transition-transform duration-700" />
            </motion.div>
          ))}
        </motion.div>

        {/* Bottom Call to Action */}
        <motion.div
          className="text-center mt-16"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={containerVariants}
        >
          <motion.p 
            className="text-snow/60 mb-6"
            variants={itemVariants}
          >
            Ready to experience these values in action?
          </motion.p>
          <motion.div variants={itemVariants}>
            <Button
              className="bg-accent hover:bg-accent/90 text-white px-6 py-3 rounded-full font-medium transition-all duration-300 group"
            >
              Join Our Mission
              <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
            </Button>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default ValuesCarousel;
