import React, { useRef } from 'react';
import { motion, useScroll, useTransform, useInView } from 'framer-motion';
import { Brain, Users, Lightbulb, Target, Rocket, Sparkles, ArrowRight, Zap } from 'lucide-react';

const Mission = () => {
  const containerRef = useRef(null);
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start end", "end start"]
  });
  
  const timelineProgress = useTransform(scrollYProgress, [0.1, 0.9], [0, 100]);
  const backgroundY = useTransform(scrollYProgress, [0, 1], ["0%", "50%"]);
  const headerY = useTransform(scrollYProgress, [0, 0.3], [0, -50]);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, x: -60, y: 20 },
    visible: {
      opacity: 1,
      x: 0,
      y: 0,
      transition: {
        duration: 0.8,
        ease: [0.22, 1, 0.36, 1]
      }
    }
  };

  const rightItemVariants = {
    hidden: { opacity: 0, x: 60, y: 20 },
    visible: {
      opacity: 1,
      x: 0,
      y: 0,
      transition: {
        duration: 0.8,
        ease: [0.22, 1, 0.36, 1]
      }
    }
  };

  const nodeVariants = {
    hidden: { scale: 0, rotate: -180, opacity: 0 },
    visible: {
      scale: 1,
      rotate: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 200,
        damping: 20,
        delay: 0.3
      }
    }
  };

  const timelineData = [
    {
      icon: Brain,
      title: "Our Foundation",
      subtitle: "Where Intelligence Converges",
      content: "The Sainpse Institute of augmented intelligence where human ingenuity and artificial intelligence converge to unlock extraordinary possibilities.",
      highlight: "Inspired by the brain's synapses, the essence of connection and emergence",
      additionalText: "we believe intelligent systems are not limited to technology but extend to society and organizations working in harmony.",
      side: "left",
      color: "from-white/5 via-accent/10 to-white/5",
      glowColor: "shadow-accent/10",
      badge: "Genesis",
      stats: "2024 - Present"
    },
    {
      icon: Users,
      title: "Our Mission",
      subtitle: "Empowering Collaboration",
      content: "Our mission is to empower individuals, business and communities to collaborate in ways that",
      highlight: "amplify intelligence, foster innovation, ease connection,",
      additionalText: "and drive transformative progress. By leveraging cutting-edge Augmented/AI tools and embracing the collective potential of networks, we aim to advance knowledge, spark creativity, and shape a future where intelligence is truly emergent at all levels.",
      side: "right",
      color: "from-white/5 via-accent/10 to-white/5",
      glowColor: "shadow-accent/10",
      badge: "Vision",
      stats: "Global Impact"
    },
    {
      icon: Rocket,
      title: "Our Movement",
      subtitle: "Beyond Philosophy",
      content: "Sainpse is more than a philosophy—it's a movement to inspire systems, both human and digital, to achieve more together than they ever could alone.",
      highlight: "A movement to inspire systems",
      additionalText: "",
      side: "left",
      color: "from-white/5 via-accent/10 to-white/5",
      glowColor: "shadow-accent/10",
      badge: "Future",
      stats: "Infinite Possibilities"
    }
  ];

  return (
    <section 
      ref={containerRef}
      id="mission" 
      className="py-20 md:py-32 bg-gradient-to-b from-midnight via-darkShade to-midnight relative overflow-hidden"
    >
      {/* Enhanced animated background decoration */}
      <motion.div 
        className="absolute inset-0"
        style={{ y: backgroundY }}
      >
        <motion.div 
          className="absolute inset-0 opacity-20"
          animate={{
            background: [
              "radial-gradient(circle at 20% 20%, rgba(56, 173, 169, 0.08) 0%, transparent 50%), radial-gradient(circle at 80% 80%, rgba(56, 173, 169, 0.05) 0%, transparent 50%)",
              "radial-gradient(circle at 80% 20%, rgba(56, 173, 169, 0.08) 0%, transparent 50%), radial-gradient(circle at 20% 80%, rgba(56, 173, 169, 0.05) 0%, transparent 50%)",
              "radial-gradient(circle at 50% 50%, rgba(56, 173, 169, 0.08) 0%, transparent 50%), radial-gradient(circle at 30% 70%, rgba(56, 173, 169, 0.05) 0%, transparent 50%)",
              "radial-gradient(circle at 20% 20%, rgba(56, 173, 169, 0.08) 0%, transparent 50%), radial-gradient(circle at 80% 80%, rgba(56, 173, 169, 0.05) 0%, transparent 50%)",
            ]
          }}
          transition={{ duration: 18, repeat: Infinity }}
        />
        
        {/* Minimal floating particles */}
        <motion.div className="absolute inset-0">
          {[...Array(12)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-1 h-1 bg-accent/20 rounded-full"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                y: [-15, 15, -15],
                opacity: [0.2, 0.4, 0.2],
                scale: [1, 1.2, 1],
              }}
              transition={{
                duration: 4 + Math.random() * 3,
                repeat: Infinity,
                delay: Math.random() * 3,
              }}
            />
          ))}
        </motion.div>
      </motion.div>

      <div className="container mx-auto px-6 lg:px-8 relative z-10">
        {/* Enhanced Header */}
        <motion.div
          className="text-center mb-20 md:mb-28"
          style={{ y: headerY }}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={containerVariants}
        >
          <motion.div 
            className="inline-flex items-center gap-3 px-4 py-2 md:px-6 md:py-3 rounded-full bg-white/5 border border-accent/20 mb-6 md:mb-8 backdrop-blur-sm"
            variants={itemVariants}
            whileHover={{ scale: 1.02, y: -1 }}
          >
            <Target className="w-4 h-4 md:w-5 md:h-5 text-accent/80" />
            <span className="text-sm md:text-base font-medium text-accent/80">Our Story</span>
          </motion.div>

          <motion.h2 
            className="text-3xl md:text-5xl lg:text-6xl font-bold mb-4 md:mb-6 text-snow px-4"
            variants={itemVariants}
          >
            Our Mission{' '}
            <motion.span 
              className="text-accent"
              animate={{ 
                opacity: [1, 0.8, 1]
              }}
              transition={{ duration: 4, repeat: Infinity }}
            >
              Timeline
            </motion.span>
          </motion.h2>

          <motion.p 
            className="text-lg md:text-xl lg:text-2xl text-snow/60 max-w-4xl mx-auto px-4 leading-relaxed"
            variants={itemVariants}
          >
            The journey of how we envision the future of{' '}
            <span className="text-snow/80 font-medium">augmented intelligence</span>
          </motion.p>
        </motion.div>

        {/* Enhanced Timeline */}
        <div className="relative max-w-7xl mx-auto">
          {/* Desktop Timeline Line with minimal glow */}
          <div className="hidden md:block absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-gradient-to-b from-accent/20 via-accent/30 to-accent/20 rounded-full"></div>
          
          {/* Desktop Animated Progress Line */}
          <motion.div
            className="hidden md:block absolute left-1/2 transform -translate-x-1/2 w-1 rounded-full origin-top overflow-hidden"
            style={{
              height: `${timelineProgress}%`,
            }}
          >
            <motion.div
              className="w-full h-full bg-accent"
              animate={{
                opacity: [0.8, 1, 0.8]
              }}
              transition={{ duration: 3, repeat: Infinity }}
            />
          </motion.div>

          {/* Desktop Timeline Items */}
          <div className="hidden md:block space-y-32">
            {timelineData.map((item, index) => {
              const isLeft = item.side === 'left';
              return (
                <motion.div
                  key={index}
                  className={`relative flex items-center ${
                    isLeft ? 'justify-start' : 'justify-end'
                  }`}
                  initial="hidden"
                  whileInView="visible"
                  viewport={{ once: true, margin: "-200px" }}
                  variants={containerVariants}
                >
                  {/* Minimal Timeline Node */}
                  <motion.div
                    className={`absolute left-1/2 transform -translate-x-1/2 w-16 h-16 rounded-full bg-gradient-to-br from-accent/90 to-accent border-4 border-darkShade flex items-center justify-center z-20 shadow-lg`}
                    variants={nodeVariants}
                    whileHover={{ 
                      scale: 1.1, 
                      rotate: 5,
                      boxShadow: "0 0 20px rgba(56, 173, 169, 0.4)"
                    }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <motion.div
                      className="relative"
                      whileHover={{ rotate: 180 }}
                      transition={{ duration: 0.4 }}
                    >
                      <item.icon className="w-8 h-8 text-white" />
                    </motion.div>
                    
                    {/* Subtle pulsing ring */}
                    <motion.div
                      className="absolute inset-0 rounded-full border-1 border-accent/30"
                      animate={{
                        scale: [1, 1.2, 1],
                        opacity: [0.3, 0, 0.3],
                      }}
                      transition={{
                        duration: 3,
                        repeat: Infinity,
                        delay: index * 0.4
                      }}
                    />
                  </motion.div>

                  {/* Enhanced Content Card */}
                  <motion.div
                    className={`w-5/12 ${isLeft ? 'mr-12' : 'ml-12'}`}
                    variants={isLeft ? itemVariants : rightItemVariants}
                  >
                    <motion.div
                      className={`relative p-8 rounded-2xl bg-white/5 backdrop-blur-sm border border-white/10 hover:border-accent/30 transition-all duration-500 overflow-hidden group cursor-pointer`}
                      whileHover={{ 
                        y: -6, 
                        scale: 1.02,
                      }}
                      whileTap={{ scale: 0.98 }}
                    >
                      {/* Minimal background gradient on hover */}
                      <motion.div 
                        className={`absolute inset-0 bg-gradient-to-br ${item.color} opacity-0 group-hover:opacity-100 transition-opacity duration-500`}
                      />
                      
                      {/* Content */}
                      <div className="relative z-10">
                        {/* Badge and Stats */}
                        <div className="flex items-center justify-between mb-6">
                          <motion.span 
                            className="inline-flex items-center gap-2 px-3 py-1.5 rounded-full bg-accent/20 border border-accent/20 text-xs font-medium text-accent backdrop-blur-sm"
                            whileHover={{ scale: 1.02 }}
                          >
                            <Zap className="w-2.5 h-2.5" />
                            {item.badge}
                          </motion.span>
                          <motion.span 
                            className="text-xs text-snow/50 font-medium"
                            initial={{ opacity: 0, x: 10 }}
                            whileInView={{ opacity: 1, x: 0 }}
                            transition={{ delay: 0.4 }}
                          >
                            {item.stats}
                          </motion.span>
                        </div>

                        {/* Header */}
                        <div className="mb-6">
                          <motion.h3 
                            className="text-2xl font-bold text-snow mb-2 group-hover:text-accent transition-colors duration-300"
                            initial={{ opacity: 0, y: 20 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.3 }}
                          >
                            {item.title}
                          </motion.h3>
                          <motion.p 
                            className="text-sm font-medium text-accent/80 group-hover:text-accent transition-colors duration-300 uppercase tracking-wider"
                            initial={{ opacity: 0, y: 20 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.4 }}
                          >
                            {item.subtitle}
                          </motion.p>
                        </div>

                        {/* Main Content */}
                        <motion.div
                          className="space-y-4"
                          initial={{ opacity: 0 }}
                          whileInView={{ opacity: 1 }}
                          transition={{ delay: 0.5 }}
                        >
                          <p className="text-snow/80 leading-relaxed group-hover:text-snow/90 transition-colors duration-300 text-base">
                            {item.content}{' '}
                            {item.highlight && (
                              <motion.span 
                                className="text-accent font-medium inline-block"
                                whileHover={{ scale: 1.02 }}
                                transition={{ duration: 0.2 }}
                              >
                                {item.highlight}
                              </motion.span>
                            )}
                            {item.additionalText && (
                              <span className="text-snow/80 group-hover:text-snow/90 transition-colors duration-300">
                                {' '}{item.additionalText}
                              </span>
                            )}
                          </p>
                        </motion.div>
                      </div>

                      {/* Minimal decorative elements */}
                      <div className="absolute -top-6 -right-6 w-16 h-16 rounded-full bg-accent/5 group-hover:bg-accent/10 transition-colors duration-500" />

                      {/* Minimal Connection Line to Timeline */}
                      <motion.div 
                        className={`absolute top-1/2 ${
                          isLeft ? 'right-0 translate-x-full' : 'left-0 -translate-x-full'
                        } w-8 h-px bg-accent/20 group-hover:bg-accent/40 transition-all duration-300`}
                        whileHover={{ scaleX: 1.1 }}
                      />
                    </motion.div>
                  </motion.div>

                  {/* Side spacing for desktop */}
                  <div className={`w-5/12 ${isLeft ? 'ml-12' : 'mr-12'}`} />
                </motion.div>
              );
            })}
          </div>

          {/* Minimal Desktop End Node */}
          <motion.div
            className="hidden md:flex relative justify-center mt-20"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={containerVariants}
          >
            <motion.div
              className="relative w-20 h-20 rounded-full bg-gradient-to-br from-accent/90 to-accent border-4 border-darkShade flex items-center justify-center shadow-lg z-10"
              variants={nodeVariants}
              whileHover={{ 
                scale: 1.1,
                boxShadow: "0 0 25px rgba(56, 173, 169, 0.5)"
              }}
              whileTap={{ scale: 0.95 }}
            >
              <motion.div
                animate={{ 
                  rotate: 360,
                  scale: [1, 1.05, 1]
                }}
                transition={{ 
                  rotate: { duration: 10, repeat: Infinity, ease: "linear" },
                  scale: { duration: 3, repeat: Infinity }
                }}
              >
                <Sparkles className="w-10 h-10 text-white" />
              </motion.div>
              
              {/* Single pulsing ring */}
              <motion.div
                className="absolute inset-0 rounded-full border-1 border-accent/30"
                animate={{
                  scale: [1, 1.3, 1],
                  opacity: [0.4, 0, 0.4],
                }}
                transition={{
                  duration: 4,
                  repeat: Infinity,
                }}
              />
            </motion.div>
            
            <motion.div
              className="absolute -bottom-10 text-center"
              variants={itemVariants}
            >
              <p className="text-base text-accent font-medium mb-1">The Future Begins</p>
              <p className="text-sm text-snow/50">Where possibilities become reality</p>
            </motion.div>
          </motion.div>
        </div>

        {/* Enhanced Mobile Timeline */}
        <div className="md:hidden">
          <div className="relative pl-2">
            {/* Mobile Timeline Line */}
            <div className="absolute left-8 top-0 w-1 h-full bg-gradient-to-b from-accent/20 via-accent/30 to-accent/20 rounded-full"></div>
            
            <div className="space-y-12">
              {timelineData.map((item, index) => (
                <motion.div
                  key={`mobile-${index}`}
                  className="relative flex items-start"
                  initial="hidden"
                  whileInView="visible"
                  viewport={{ once: true, margin: "-50px" }}
                  variants={itemVariants}
                >
                  {/* Minimal Mobile Timeline Node */}
                  <motion.div
                    className={`w-12 h-12 rounded-full bg-gradient-to-br from-accent/90 to-accent border-3 border-darkShade flex items-center justify-center z-10 ${item.glowColor} shadow-lg mr-6 flex-shrink-0`}
                    variants={nodeVariants}
                    whileHover={{ 
                      scale: 1.1,
                      boxShadow: "0 0 20px rgba(56, 173, 169, 0.4)"
                    }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <motion.div
                      whileHover={{ rotate: 180 }}
                      transition={{ duration: 0.3 }}
                    >
                      <item.icon className="w-6 h-6 text-white drop-shadow-sm" />
                    </motion.div>
                    
                    {/* Mobile single pulsing ring */}
                    <motion.div
                      className="absolute inset-0 rounded-full border-2 border-accent/30"
                      animate={{
                        scale: [1, 1.3, 1],
                        opacity: [0.3, 0, 0.3],
                      }}
                      transition={{
                        duration: 3,
                        repeat: Infinity,
                        delay: index * 0.3
                      }}
                    />
                  </motion.div>

                  {/* Minimal Mobile Content */}
                  <motion.div
                    className="flex-1 p-6 rounded-2xl bg-white/5 backdrop-blur-sm border border-white/10 hover:border-accent/30 transition-all duration-500 overflow-hidden group"
                    variants={itemVariants}
                    whileHover={{ y: -4, scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    {/* Mobile background gradient */}
                    <motion.div 
                      className={`absolute inset-0 bg-gradient-to-br ${item.color} opacity-0 group-hover:opacity-100 transition-opacity duration-500`}
                    />
                    
                    <div className="relative z-10">
                      {/* Mobile badge */}
                      <motion.div 
                        className="flex items-center justify-between mb-4"
                        initial={{ opacity: 0, y: 10 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.2 }}
                      >
                        <span className="inline-flex items-center gap-1.5 px-2.5 py-1 rounded-full bg-accent/20 border border-accent/30 text-xs font-semibold text-accent">
                          <Zap className="w-2.5 h-2.5" />
                          {item.badge}
                        </span>
                        <span className="text-xs text-snow/60 font-medium">{item.stats}</span>
                      </motion.div>
                      
                      <h3 className="text-xl font-bold text-snow mb-2 group-hover:text-accent transition-colors duration-300">{item.title}</h3>
                      <p className="text-xs font-medium text-accent/80 mb-4 uppercase tracking-wider group-hover:text-accent transition-colors duration-300">{item.subtitle}</p>
                      <p className="text-snow/80 leading-relaxed text-sm group-hover:text-snow/90 transition-colors duration-300">
                        {item.content}{' '}
                        {item.highlight && (
                          <span className="text-accent font-medium">{item.highlight}</span>
                        )}
                        {item.additionalText && (
                          <span className="text-snow/80 group-hover:text-snow/90 transition-colors duration-300"> {item.additionalText}</span>
                        )}
                      </p>
                    </div>
                    
                    {/* Mobile minimal decorative elements */}
                    <div className="absolute -top-4 -right-4 w-12 h-12 rounded-full bg-accent/5 group-hover:bg-accent/10 transition-all duration-500" />
                  </motion.div>
                </motion.div>
              ))}
              
              {/* Minimal Mobile End Node */}
              <motion.div
                className="relative flex items-center pl-2"
                initial="hidden"
                whileInView="visible"
                viewport={{ once: true, margin: "-50px" }}
                variants={itemVariants}
              >
                <motion.div
                  className="w-14 h-14 rounded-full bg-gradient-to-br from-accent/90 to-accent border-3 border-darkShade flex items-center justify-center shadow-lg mr-6"
                  variants={nodeVariants}
                  whileHover={{ 
                    scale: 1.1,
                    boxShadow: "0 0 25px rgba(56, 173, 169, 0.5)"
                  }}
                  whileTap={{ scale: 0.95 }}
                >
                  <motion.div
                    animate={{ 
                      rotate: 360,
                      scale: [1, 1.1, 1]
                    }}
                    transition={{ 
                      rotate: { duration: 6, repeat: Infinity, ease: "linear" },
                      scale: { duration: 2, repeat: Infinity }
                    }}
                  >
                    <Sparkles className="w-7 h-7 text-white drop-shadow-sm" />
                  </motion.div>
                  
                  {/* Mobile single pulsing ring */}
                  <motion.div
                    className="absolute inset-0 rounded-full border-2 border-accent/30"
                    animate={{
                      scale: [1, 1.3, 1],
                      opacity: [0.4, 0, 0.4],
                    }}
                    transition={{
                      duration: 4,
                      repeat: Infinity,
                    }}
                  />
                </motion.div>
                
                <motion.div
                  className="flex-1"
                  variants={itemVariants}
                >
                  <p className="text-base text-accent font-bold mb-0.5">The Future Begins</p>
                  <p className="text-xs text-snow/60">Where possibilities become reality</p>
                </motion.div>
              </motion.div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Mission;