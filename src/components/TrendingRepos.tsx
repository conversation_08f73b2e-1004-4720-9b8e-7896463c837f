import React, { useState, useEffect, useRef, useMemo } from 'react';
import { motion, useScroll, useTransform } from 'framer-motion';
import {
  Star,
  GitFork,
  Calendar,
  Code2,
  ExternalLink,
  Sparkles,
  AlertCircle,
  RefreshCw,
  Brain,
  Rocket,
  Hexagon,
  Triangle,
  Circle,
  Square
} from 'lucide-react';
import { But<PERSON> } from './ui/button';

interface Repository {
  id: number;
  name: string;
  full_name: string;
  description: string;
  html_url: string;
  stargazers_count: number;
  forks_count: number;
  language: string;
  created_at: string;
  updated_at: string;
  topics: string[];
  owner: {
    login: string;
    avatar_url: string;
  };
}

// Fallback data for when GitHub API is unavailable
const getFallbackRepos = (): Repository[] => [
  {
    id: 1,
    name: 'tensorflow',
    full_name: 'tensorflow/tensorflow',
    description: 'An Open Source Machine Learning Framework for Everyone',
    html_url: 'https://github.com/tensorflow/tensorflow',
    stargazers_count: 185000,
    forks_count: 74000,
    language: 'C++',
    created_at: '2015-11-07T01:05:27Z',
    updated_at: '2024-01-15T10:30:00Z',
    topics: ['machine-learning', 'deep-learning', 'tensorflow', 'python'],
    owner: {
      login: 'tensorflow',
      avatar_url: 'https://avatars.githubusercontent.com/u/15658638?v=4'
    }
  },
  {
    id: 2,
    name: 'pytorch',
    full_name: 'pytorch/pytorch',
    description: 'Tensors and Dynamic neural networks in Python with strong GPU acceleration',
    html_url: 'https://github.com/pytorch/pytorch',
    stargazers_count: 82000,
    forks_count: 22000,
    language: 'Python',
    created_at: '2016-08-13T01:05:27Z',
    updated_at: '2024-01-15T08:20:00Z',
    topics: ['deep-learning', 'machine-learning', 'pytorch', 'python'],
    owner: {
      login: 'pytorch',
      avatar_url: 'https://avatars.githubusercontent.com/u/21003710?v=4'
    }
  },
  {
    id: 3,
    name: 'transformers',
    full_name: 'huggingface/transformers',
    description: 'State-of-the-art Machine Learning for Pytorch, TensorFlow, and JAX',
    html_url: 'https://github.com/huggingface/transformers',
    stargazers_count: 133000,
    forks_count: 26000,
    language: 'Python',
    created_at: '2018-10-29T01:05:27Z',
    updated_at: '2024-01-15T12:45:00Z',
    topics: ['transformers', 'nlp', 'pytorch', 'tensorflow', 'machine-learning'],
    owner: {
      login: 'huggingface',
      avatar_url: 'https://avatars.githubusercontent.com/u/25720743?v=4'
    }
  },
  {
    id: 4,
    name: 'scikit-learn',
    full_name: 'scikit-learn/scikit-learn',
    description: 'scikit-learn: machine learning in Python',
    html_url: 'https://github.com/scikit-learn/scikit-learn',
    stargazers_count: 59000,
    forks_count: 25000,
    language: 'Python',
    created_at: '2010-08-17T01:05:27Z',
    updated_at: '2024-01-15T14:20:00Z',
    topics: ['machine-learning', 'python', 'scikit-learn', 'data-science'],
    owner: {
      login: 'scikit-learn',
      avatar_url: 'https://avatars.githubusercontent.com/u/365630?v=4'
    }
  },
  {
    id: 5,
    name: 'keras',
    full_name: 'keras-team/keras',
    description: 'Deep Learning for humans',
    html_url: 'https://github.com/keras-team/keras',
    stargazers_count: 61000,
    forks_count: 19000,
    language: 'Python',
    created_at: '2015-03-27T01:05:27Z',
    updated_at: '2024-01-15T11:10:00Z',
    topics: ['deep-learning', 'keras', 'machine-learning', 'neural-networks'],
    owner: {
      login: 'keras-team',
      avatar_url: 'https://avatars.githubusercontent.com/u/34455048?v=4'
    }
  },
  {
    id: 6,
    name: 'stable-diffusion',
    full_name: 'CompVis/stable-diffusion',
    description: 'A latent text-to-image diffusion model',
    html_url: 'https://github.com/CompVis/stable-diffusion',
    stargazers_count: 67000,
    forks_count: 10000,
    language: 'Python',
    created_at: '2022-08-10T01:05:27Z',
    updated_at: '2024-01-15T16:30:00Z',
    topics: ['stable-diffusion', 'generative-ai', 'diffusion-models', 'text-to-image'],
    owner: {
      login: 'CompVis',
      avatar_url: 'https://avatars.githubusercontent.com/u/54370586?v=4'
    }
  }
];

const TrendingRepos = () => {
  const [repos, setRepos] = useState<Repository[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [usingFallback, setUsingFallback] = useState(false);
  const [hoveredCard, setHoveredCard] = useState<number | null>(null);
  const [activeNeuralNodes, setActiveNeuralNodes] = useState<number[]>([]);

  const containerRef = useRef(null);
  const neuralNetworkRef = useRef(null);

  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start end", "end start"]
  });

  const backgroundY = useTransform(scrollYProgress, [0, 1], ["0%", "50%"]);
  const headerY = useTransform(scrollYProgress, [0, 0.3], [0, -50]);

  // Minimal background elements
  const backgroundElements = useMemo(() => {
    return Array.from({ length: 12 }, (_, i) => ({
      id: i,
      x: Math.random() * 100,
      y: Math.random() * 100,
      size: Math.random() * 2 + 1,
      delay: Math.random() * 3
    }));
  }, []);

  // Activate background elements based on scroll and interaction
  useEffect(() => {
    const interval = setInterval(() => {
      setActiveNeuralNodes(() => {
        const newActive = Array.from({ length: Math.floor(Math.random() * 4) + 2 }, () =>
          Math.floor(Math.random() * backgroundElements.length)
        );
        return newActive;
      });
    }, 3000);

    return () => clearInterval(interval);
  }, [backgroundElements.length]);

  const fetchTrendingRepos = async () => {
    try {
      
      // Try multiple search strategies for better success rate
      const searchQueries = [
        'topic:artificial-intelligence',
        'topic:machine-learning',
        'topic:deep-learning',
        'language:Python machine learning',
        'language:Python AI',
        'tensorflow OR pytorch OR keras'
      ];
      
      const sort = 'stars';
      const order = 'desc';
      const per_page = 12;
      
      let response: Response | undefined;
      let lastError = null;
      
      // Try each query until one succeeds
      for (const query of searchQueries) {
        try {
          response = await fetch(
            `https://api.github.com/search/repositories?q=${encodeURIComponent(query)}&sort=${sort}&order=${order}&per_page=${per_page}`,
            {
              headers: {
                'Accept': 'application/vnd.github.v3+json',
                'User-Agent': 'Sainpse-Landing-Page',
                // Add your GitHub token here for higher rate limits
                ...(import.meta.env.VITE_GITHUB_TOKEN && {
                  'Authorization': `token ${import.meta.env.VITE_GITHUB_TOKEN}`
                })
              }
            }
          );

          if (response.ok) {
            break; // Success, exit the loop
          } else {
            lastError = `GitHub API Error: ${response.status} ${response.statusText}`;
            console.warn(`Query failed: ${query} - ${lastError}`);
          }
        } catch (queryError) {
          lastError = queryError instanceof Error ? queryError.message : 'Network error';
          console.warn(`Query failed: ${query} - ${lastError}`);
        }
      }

      if (!response || !response.ok) {
        // If all queries failed, use fallback data
        console.warn('All GitHub API queries failed, using fallback data');
        setRepos(getFallbackRepos());
        setUsingFallback(true);
        return;
      }

      const data = await response.json();
      setRepos(data.items || []);
      setUsingFallback(false);
    } catch (err) {
      console.error('Error fetching trending repos:', err);
      // Use fallback data instead of showing error
      setRepos(getFallbackRepos());
      setUsingFallback(true);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchTrendingRepos();
  }, []);

  const handleRefresh = () => {
    setRefreshing(true);
    fetchTrendingRepos();
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getLanguageColor = (language: string) => {
    const colors: Record<string, string> = {
      'Python': '#3776ab',
      'JavaScript': '#f1e05a',
      'TypeScript': '#2b7489',
      'Java': '#b07219',
      'C++': '#f34b7d',
      'Go': '#00ADD8',
      'Rust': '#dea584',
      'Swift': '#fa7343',
      'Kotlin': '#F18E33',
      'R': '#198ce7',
      'Jupyter Notebook': '#DA5B0B',
      'C': '#555555',
      'C#': '#239120',
      'PHP': '#4F5D95',
      'Ruby': '#701516',
    };
    return colors[language] || '#8b949e';
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.1
      }
    }
  };

  const fadeInUp = {
    hidden: { y: 30, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.8,
        ease: [0.22, 1, 0.36, 1]
      }
    }
  };

  const cardVariants = {
    hidden: { y: 40, opacity: 0, scale: 0.95 },
    visible: {
      y: 0,
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.6,
        ease: [0.22, 1, 0.36, 1]
      }
    }
  };

  return (
    <section
      ref={containerRef}
      id="trending-repos"
      className="py-20 md:py-32 bg-gradient-to-b from-midnight via-darkShade to-midnight relative overflow-hidden perspective-1000"
    >
      {/* Minimal Clean Background */}
      <motion.div
        ref={neuralNetworkRef}
        className="absolute inset-0 pointer-events-none"
        style={{ y: backgroundY }}
      >
        {/* Subtle gradient overlay */}
        <motion.div
          className="absolute inset-0 opacity-20"
          animate={{
            background: [
              "radial-gradient(circle at 20% 20%, rgba(56, 173, 169, 0.05) 0%, transparent 50%)",
              "radial-gradient(circle at 80% 80%, rgba(56, 173, 169, 0.05) 0%, transparent 50%)",
              "radial-gradient(circle at 20% 20%, rgba(56, 173, 169, 0.05) 0%, transparent 50%)"
            ]
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />

        {/* Minimal floating elements */}
        <motion.div className="absolute inset-0">
          {backgroundElements.map((element) => (
            <motion.div
              key={element.id}
              className="absolute w-1 h-1 bg-accent/20 rounded-full"
              style={{
                left: `${element.x}%`,
                top: `${element.y}%`,
              }}
              animate={{
                y: [-10, 10, -10],
                opacity: activeNeuralNodes.includes(element.id) ? [0.2, 0.6, 0.2] : [0.1, 0.3, 0.1],
                scale: activeNeuralNodes.includes(element.id) ? [1, 1.5, 1] : [1, 1.2, 1],
              }}
              transition={{
                duration: 6 + element.delay,
                repeat: Infinity,
                ease: "easeInOut",
                delay: element.delay
              }}
            />
          ))}
        </motion.div>
      </motion.div>

      <div className="max-w-7xl mx-auto relative z-10 px-6 lg:px-8">
        {/* Revolutionary Holographic Header */}
        <motion.div
          className="text-center mb-16 md:mb-24 relative"
          style={{ y: headerY }}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={containerVariants}
        >
          {/* Floating Geometric Elements */}
          <div className="absolute inset-0 pointer-events-none">
            {[Hexagon, Triangle, Circle, Square].map((Icon, i) => (
              <motion.div
                key={i}
                className="absolute"
                style={{
                  left: `${20 + i * 20}%`,
                  top: `${10 + (i % 2) * 60}%`,
                }}
                animate={{
                  rotate: [0, 360],
                  scale: [0.8, 1.2, 0.8],
                  opacity: [0.1, 0.3, 0.1]
                }}
                transition={{
                  duration: 8 + i * 2,
                  repeat: Infinity,
                  ease: "easeInOut",
                  delay: i * 0.5
                }}
              >
                <Icon className="w-8 h-8 text-accent/20" />
              </motion.div>
            ))}
          </div>

          {/* Quantum Badge */}
          <motion.div
            className="inline-flex items-center gap-3 px-6 py-3 rounded-full bg-gradient-to-r from-accent/10 via-accent/5 to-accent/10 border border-accent/30 mb-8 backdrop-blur-md relative overflow-hidden"
            variants={fadeInUp}
            whileHover={{
              scale: 1.05,
              y: -2,
              boxShadow: "0 0 30px rgba(56, 173, 169, 0.3)"
            }}
          >
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-transparent via-accent/10 to-transparent"
              animate={{
                x: ['-100%', '100%']
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
            <Brain className="w-5 h-5 text-accent relative z-10" />
            <span className="text-base font-medium text-accent relative z-10">Neural AI Discoveries</span>
            <Sparkles className="w-4 h-4 text-accent/70 relative z-10" />
          </motion.div>

          {/* Holographic Title */}
          <motion.h2
            className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 text-snow px-4 relative"
            variants={fadeInUp}
          >
            <motion.span
              className="inline-block"
              animate={{
                textShadow: [
                  "0 0 10px rgba(56, 173, 169, 0.5)",
                  "0 0 20px rgba(56, 173, 169, 0.8)",
                  "0 0 10px rgba(56, 173, 169, 0.5)"
                ]
              }}
              transition={{ duration: 2, repeat: Infinity }}
            >
              Quantum
            </motion.span>{' '}
            <motion.span
              className="text-accent inline-block relative"
              animate={{
                opacity: [1, 0.7, 1],
                scale: [1, 1.02, 1]
              }}
              transition={{ duration: 3, repeat: Infinity }}
            >
              AI Nexus
              <motion.div
                className="absolute -inset-1 bg-gradient-to-r from-accent/20 via-transparent to-accent/20 blur-sm"
                animate={{
                  opacity: [0, 0.5, 0]
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              />
            </motion.span>
          </motion.h2>

          {/* Futuristic Description */}
          <motion.p
            className="text-xl md:text-2xl lg:text-3xl text-snow/70 max-w-5xl mx-auto px-4 leading-relaxed mb-8 relative"
            variants={fadeInUp}
          >
            <motion.span
              className="inline-block"
              animate={{
                color: ["rgba(255, 255, 255, 0.7)", "rgba(56, 173, 169, 0.9)", "rgba(255, 255, 255, 0.7)"]
              }}
              transition={{ duration: 4, repeat: Infinity }}
            >
              Explore the quantum frontier
            </motion.span>{' '}
            of artificial intelligence through{' '}
            <span className="text-accent font-medium">revolutionary repositories</span>{' '}
            that are reshaping our digital reality
            {usingFallback && (
              <motion.span
                className="block text-lg text-yellow-400/80 mt-3 font-medium"
                animate={{ opacity: [0.6, 1, 0.6] }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                ⚡ Quantum Cache Active • Refresh for Live Neural Data
              </motion.span>
            )}
          </motion.p>

          <motion.div
            className="flex justify-center"
            variants={fadeInUp}
          >
            <Button
              onClick={handleRefresh}
              disabled={refreshing}
              variant="outline"
              className="border-accent/30 text-accent hover:bg-accent hover:text-white transition-all duration-300"
            >
              <RefreshCw className={`w-4 h-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
              {refreshing ? 'Refreshing...' : 'Refresh'}
            </Button>
          </motion.div>
        </motion.div>

        {/* Content */}
        {loading ? (
          <div className="flex justify-center items-center py-20">
            <motion.div
              className="flex items-center space-x-3"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
            >
              <RefreshCw className="w-6 h-6 text-accent animate-spin" />
              <span className="text-snow/60">Loading trending repositories...</span>
            </motion.div>
          </div>
        ) : repos.length === 0 ? (
          <motion.div
            className="flex flex-col items-center justify-center py-20"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
          >
            <AlertCircle className="w-12 h-12 text-yellow-400 mb-4" />
            <h3 className="text-xl font-semibold text-snow mb-2">Unable to Load Latest Data</h3>
            <p className="text-snow/60 text-center max-w-md mb-6">
              Showing popular AI repositories. Try refreshing for the latest data.
            </p>
            <Button
              onClick={handleRefresh}
              className="bg-accent hover:bg-accent/90 text-white"
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Refresh
            </Button>
          </motion.div>
        ) : (
          <motion.div
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 md:gap-8"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={containerVariants}
          >
            {repos.map((repo, repoIndex) => (
              <motion.div
                key={repo.id}
                className="group relative perspective-1000"
                variants={cardVariants}
                onHoverStart={() => setHoveredCard(repoIndex)}
                onHoverEnd={() => setHoveredCard(null)}
                whileHover={{
                  y: -12,
                  transition: { duration: 0.4, ease: "easeOut" }
                }}
              >
                {/* Compact Modern Card Container */}
                <motion.div
                  className="relative bg-gradient-to-br from-white/6 via-white/3 to-white/6 backdrop-blur-lg border border-accent/15 rounded-2xl p-6 overflow-hidden transform-gpu"
                  style={{
                    transformStyle: "preserve-3d"
                  }}
                  whileHover={{
                    rotateX: 2,
                    rotateY: 2,
                    scale: 1.02,
                    boxShadow: "0 15px 30px rgba(56, 173, 169, 0.15), 0 0 0 1px rgba(56, 173, 169, 0.1)"
                  }}
                  transition={{ duration: 0.3, ease: "easeOut" }}
                >
                  {/* Subtle Shimmer Effect */}
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-r from-transparent via-accent/5 to-transparent opacity-0 group-hover:opacity-100"
                    animate={hoveredCard === repoIndex ? {
                      x: ['-100%', '100%']
                    } : {}}
                    transition={{
                      duration: 2,
                      repeat: hoveredCard === repoIndex ? Infinity : 0,
                      ease: "easeInOut"
                    }}
                  />

                  {/* Corner Accent */}
                  <div className="absolute top-3 right-3">
                    <motion.div
                      animate={hoveredCard === repoIndex ? {
                        rotate: [0, 90, 0],
                        scale: [1, 1.1, 1]
                      } : {}}
                      transition={{ duration: 2, repeat: Infinity }}
                    >
                      <Code2 className="w-4 h-4 text-accent/30" />
                    </motion.div>
                  </div>

                  {/* Content Layer */}
                  <div className="relative z-10">
                    {/* Compact Header */}
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center space-x-3">
                        <motion.div
                          className="relative"
                          whileHover={{ scale: 1.05 }}
                        >
                          <img
                            src={repo.owner.avatar_url}
                            alt={repo.owner.login}
                            className="w-10 h-10 rounded-full border border-accent/20"
                          />
                        </motion.div>
                        <div className="min-w-0 flex-1">
                          <motion.h3
                            className="text-lg font-bold text-snow group-hover:text-accent transition-colors duration-300 truncate"
                          >
                            {repo.name}
                          </motion.h3>
                          <p className="text-xs text-snow/60 truncate">{repo.owner.login}</p>
                        </div>
                      </div>
                    </div>

                    {/* Compact Description */}
                    <motion.p
                      className="text-snow/75 text-sm mb-4 line-clamp-2 leading-relaxed"
                    >
                      {repo.description || 'An innovative AI project'}
                    </motion.p>

                    {/* Compact Topics */}
                    {repo.topics && repo.topics.length > 0 && (
                      <div className="flex flex-wrap gap-1.5 mb-4">
                        {repo.topics.slice(0, 2).map((topic, topicIndex) => (
                          <span
                            key={topicIndex}
                            className="px-2 py-1 text-xs bg-accent/15 text-accent rounded-md border border-accent/20 font-medium"
                          >
                            {topic}
                          </span>
                        ))}
                        {repo.topics.length > 2 && (
                          <span className="px-2 py-1 text-xs bg-white/10 text-snow/60 rounded-md border border-white/15">
                            +{repo.topics.length - 2}
                          </span>
                        )}
                      </div>
                    )}

                    {/* Compact Stats */}
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center space-x-4">
                        <div className="flex items-center space-x-1.5">
                          <Star className="w-4 h-4 text-yellow-400" />
                          <span className="text-sm font-medium text-snow/80">{formatNumber(repo.stargazers_count)}</span>
                        </div>
                        <div className="flex items-center space-x-1.5">
                          <GitFork className="w-4 h-4 text-accent/70" />
                          <span className="text-sm font-medium text-snow/80">{formatNumber(repo.forks_count)}</span>
                        </div>
                      </div>
                      {repo.language && (
                        <div className="flex items-center space-x-1.5">
                          <div
                            className="w-3 h-3 rounded-full"
                            style={{ backgroundColor: getLanguageColor(repo.language) }}
                          />
                          <span className="text-xs font-medium text-snow/70">{repo.language}</span>
                        </div>
                      )}
                    </div>

                    {/* Compact Footer */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-1.5 text-xs text-snow/50">
                        <Calendar className="w-3 h-3" />
                        <span>Updated {formatDate(repo.updated_at)}</span>
                      </div>
                      <motion.a
                        href={repo.html_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center space-x-1.5 text-sm text-accent hover:text-accent/80 transition-colors duration-300 font-medium"
                        whileHover={{
                          scale: 1.05,
                          x: 2
                        }}
                        whileTap={{ scale: 0.95 }}
                      >
                        <span>View</span>
                        <ExternalLink className="w-3 h-3" />
                      </motion.a>
                    </div>
                  </div>
                </motion.div>
              </motion.div>
            ))}
          </motion.div>
        )}

        {/* Quantum Portal CTA Section */}
        <motion.div
          className="text-center mt-20 md:mt-32 relative"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={containerVariants}
        >
          {/* Quantum Portal Background */}
          <motion.div
            className="absolute inset-0 flex items-center justify-center pointer-events-none"
            animate={{
              rotate: [0, 360]
            }}
            transition={{
              duration: 20,
              repeat: Infinity,
              ease: "linear"
            }}
          >
            <div className="w-96 h-96 rounded-full border border-accent/20 opacity-30" />
            <div className="absolute w-80 h-80 rounded-full border border-accent/30 opacity-40" />
            <div className="absolute w-64 h-64 rounded-full border border-accent/40 opacity-50" />
          </motion.div>

          <motion.div
            className="relative z-10"
            variants={fadeInUp}
          >
            <motion.div
              className="inline-block relative"
              whileHover={{
                scale: 1.05,
                y: -5
              }}
              whileTap={{ scale: 0.95 }}
            >
              {/* Quantum Energy Field */}
              <motion.div
                className="absolute -inset-4 bg-gradient-to-r from-accent/20 via-accent/10 to-accent/20 rounded-full blur-xl"
                animate={{
                  scale: [1, 1.1, 1],
                  opacity: [0.3, 0.6, 0.3]
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              />

              <a
                href="https://github.com/Sainpse"
                target="_blank"
                rel="noopener noreferrer"
              >
                <Button
                  size="lg"
                  className="relative bg-gradient-to-r from-accent to-accent/80 text-black hover:from-accent/90 hover:to-accent/70 transition-all duration-500 px-12 py-6 text-lg font-bold shadow-2xl border border-accent/30"
                >
                  <motion.div
                    className="flex items-center space-x-3"
                    animate={{
                      textShadow: ["0 0 0px rgba(0, 0, 0, 0)", "0 0 10px rgba(0, 0, 0, 0.3)", "0 0 0px rgba(0, 0, 0, 0)"]
                    }}
                    transition={{ duration: 2, repeat: Infinity }}
                  >
                    <Rocket className="w-6 h-6" />
                    <span>Enter the AI Nexus</span>
                    <motion.div
                      animate={{
                        x: [0, 5, 0]
                      }}
                      transition={{ duration: 1.5, repeat: Infinity }}
                    >
                      <ExternalLink className="w-5 h-5" />
                    </motion.div>
                  </motion.div>
                </Button>
              </a>
            </motion.div>
          
          <motion.p
            className="text-xl text-snow/70 mt-8 max-w-3xl mx-auto leading-relaxed"
            variants={fadeInUp}
            animate={{
              color: ["rgba(255, 255, 255, 0.7)", "rgba(56, 173, 169, 0.9)", "rgba(255, 255, 255, 0.7)"]
            }}
            transition={{ duration: 4, repeat: Infinity }}
          >
            Step through the quantum gateway and discover our{' '}
            <span className="text-accent font-semibold">revolutionary AI ecosystem</span>{' '}
            where innovation meets infinite possibility
          </motion.p>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default TrendingRepos;
