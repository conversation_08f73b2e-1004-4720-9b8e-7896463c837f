@layer utilities {
  .scrollbar-thin {
    scrollbar-width: thin;
  }
  
  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-track {
    background: var(--scrollbar-track, rgba(255, 255, 255, 0.05));
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: var(--scrollbar-thumb, rgba(16, 185, 129, 0.5));
    border-radius: 3px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: var(--scrollbar-thumb-hover, rgba(16, 185, 129, 0.7));
  }

  .drop-shadow-glow {
    filter: drop-shadow(0 0 10px rgba(16, 185, 129, 0.3));
  }
}

.perspective-1000 {
  perspective: 1000px;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

@keyframes glow {
  0%, 100% {
    filter: drop-shadow(0 0 5px rgba(16, 185, 129, 0.3));
  }
  50% {
    filter: drop-shadow(0 0 20px rgba(16, 185, 129, 0.6));
  }
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.animate-gradient {
  animation: gradient 15s ease infinite;
  background-size: 200% 200%;
}
