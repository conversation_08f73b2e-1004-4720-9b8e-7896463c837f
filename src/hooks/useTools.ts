import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toolsService } from '../services/toolsService';

export function useTools() {
  const queryClient = useQueryClient();

  const { data: categories, isLoading: loadingCategories } = useQuery({
    queryKey: ['categories'],
    queryFn: toolsService.getCategories,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const { data: tools, isLoading: loadingTools } = useQuery({
    queryKey: ['tools'],
    queryFn: toolsService.getTools,
    staleTime: 5 * 60 * 1000,
  });

  const updateRating = useMutation({
    mutationFn: ({ toolId, score }: { toolId: string; score: number }) => 
      toolsService.updateToolRating(toolId, score),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tools'] });
    },
  });

  const addTool = useMutation({
    mutationFn: toolsService.addTool,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tools'] });
    },
  });

  const addCategory = useMutation({
    mutationFn: toolsService.addCategory,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['categories'] });
    },
  });

  const deleteTool = useMutation({
    mutationFn: toolsService.deleteTool,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tools'] });
    },
  });

  const deleteCategory = useMutation({
    mutationFn: toolsService.deleteCategory,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['categories'] });
    },
  });

  return {
    categories,
    tools,
    isLoading: loadingCategories || loadingTools,
    updateRating,
    addTool,
    addCategory,
    deleteTool,
    deleteCategory,
  };
}
