@import '@fontsource/inter/400.css';
@import '@fontsource/inter/500.css';
@import '@fontsource/inter/600.css';
@import '@fontsource/inter/700.css';
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --primary: 176 69% 45%; /* Updated to accent color */
    --primary-foreground: 144.9 80.4% 10%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 176 69% 45%; /* Updated to accent color */
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-midnight text-snow;
  }
}

html {
  scroll-behavior: smooth;
}

.glow-effect {
  box-shadow: 0 0 15px theme('colors.accent');
}

.network-node {
  width: 4px;
  height: 4px;
  background: theme('colors.accent');
  border-radius: 50%;
  position: absolute;
  animation: glow 2s infinite;
}

.network-line {
  height: 1px;
  background: linear-gradient(90deg, transparent, theme('colors.accent'), transparent);
  position: absolute;
  transform-origin: left;
}