import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, act } from '@testing-library/react';
import { useAppStore } from '@/stores/app-store';
import { usePerformanceMonitoring, useComponentPerformance } from '@/hooks/use-performance';
import React from 'react';

// Mock component that uses performance monitoring
const TestComponent: React.FC<{ componentName: string }> = ({ componentName }) => {
  const { startMeasurement, endMeasurement } = useComponentPerformance(componentName);
  
  React.useEffect(() => {
    startMeasurement();
    return endMeasurement;
  }, [startMeasurement, endMeasurement]);

  return <div>Test Component</div>;
};

// Component that triggers slow renders
const SlowComponent: React.FC = () => {
  const { measureRender } = usePerformanceMonitoring();
  
  React.useEffect(() => {
    const endMeasurement = measureRender('SlowComponent');
    
    // Simulate slow render by delaying the end measurement
    setTimeout(() => {
      endMeasurement();
    }, 20); // This will trigger optimization since it's > 16ms
    
    return endMeasurement;
  }, [measureRender]);

  return <div>Slow Component</div>;
};

describe('Infinite Loop Fix', () => {
  beforeEach(() => {
    // Reset store state before each test
    const store = useAppStore.getState();
    store.clearAnalytics();
    
    // Reset UI state
    useAppStore.setState({
      ui: {
        ...useAppStore.getState().ui,
        isOptimizing: false,
      },
      performance: {
        loadTime: 0,
        renderTime: 0,
        interactionLatency: 0,
        memoryUsage: 0,
        bundleSize: 0,
      },
    });
  });

  it('should not cause infinite loops when measuring performance', async () => {
    let renderCount = 0;
    
    const TestComponentWithCounter: React.FC = () => {
      renderCount++;
      const { measureRender } = usePerformanceMonitoring();
      
      React.useEffect(() => {
        const endMeasurement = measureRender('TestComponent');
        setTimeout(endMeasurement, 1); // Quick measurement
      }, [measureRender]);

      return <div>Render count: {renderCount}</div>;
    };

    render(<TestComponentWithCounter />);
    
    // Wait for any async operations
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 100));
    });

    // Should not have excessive re-renders
    expect(renderCount).toBeLessThan(5);
  });

  it('should throttle optimization calls', async () => {
    const optimizeSpy = vi.spyOn(useAppStore.getState(), 'optimizePerformance');
    
    // Render multiple slow components
    render(
      <div>
        <SlowComponent />
        <SlowComponent />
        <SlowComponent />
      </div>
    );

    // Wait for measurements to complete
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 100));
    });

    // Should not call optimize more than once due to throttling
    expect(optimizeSpy).toHaveBeenCalledTimes(1);
    
    optimizeSpy.mockRestore();
  });

  it('should prevent recursive optimization', () => {
    const store = useAppStore.getState();
    
    // Set optimization flag
    useAppStore.setState({
      ui: { ...store.ui, isOptimizing: true }
    });

    // Try to optimize again - should be prevented
    const result = store.optimizePerformance();
    
    // Should return early without doing anything
    expect(result).toBeUndefined();
  });

  it('should reset optimization flag after timeout', async () => {
    const store = useAppStore.getState();
    
    // Trigger optimization
    store.optimizePerformance();
    
    // Should be optimizing initially
    expect(useAppStore.getState().ui.isOptimizing).toBe(true);
    
    // Wait for timeout
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 150));
    });
    
    // Should no longer be optimizing
    expect(useAppStore.getState().ui.isOptimizing).toBe(false);
  });

  it('should handle component performance monitoring without loops', () => {
    let renderCount = 0;
    
    const MonitoredComponent: React.FC = () => {
      renderCount++;
      useComponentPerformance('MonitoredComponent');
      return <div>Monitored</div>;
    };

    const { rerender } = render(<MonitoredComponent />);
    
    // Rerender multiple times
    rerender(<MonitoredComponent />);
    rerender(<MonitoredComponent />);
    rerender(<MonitoredComponent />);

    // Should not cause excessive re-renders
    expect(renderCount).toBe(4); // Initial + 3 rerenders
  });
});
