import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import { CommandPalette } from '@/components/CommandPalette';
import { useAppStore } from '@/stores/app-store';
import { BrowserRouter } from 'react-router-dom';

// Mock the router
const MockRouter = ({ children }: { children: React.ReactNode }) => (
  <BrowserRouter>{children}</BrowserRouter>
);

describe('CommandPalette Fixes', () => {
  beforeEach(() => {
    // Reset store state before each test
    useAppStore.getState().clearAnalytics();
  });

  it('should render without errors when analytics.events is undefined', () => {
    const mockOnOpenChange = vi.fn();
    
    expect(() => {
      render(
        <MockRouter>
          <CommandPalette open={true} onOpenChange={mockOnOpenChange} />
        </MockRouter>
      );
    }).not.toThrow();
  });

  it('should handle empty analytics events gracefully', () => {
    const mockOnOpenChange = vi.fn();
    const store = useAppStore.getState();
    
    // Ensure analytics events is empty
    store.clearAnalytics();
    
    render(
      <MockRouter>
        <CommandPalette open={true} onOpenChange={mockOnOpenChange} />
      </MockRouter>
    );
    
    // Should render the command palette without errors
    expect(screen.getByPlaceholderText('Type a command or search...')).toBeInTheDocument();
  });

  it('should use cached user history to prevent infinite loops', () => {
    const mockOnOpenChange = vi.fn();
    const store = useAppStore.getState();
    
    // Add some command execution events
    store.trackEvent('command_executed', { commandId: 'nav-home' });
    store.trackEvent('command_executed', { commandId: 'nav-tools' });
    
    // Render multiple times to test caching
    const { rerender } = render(
      <MockRouter>
        <CommandPalette open={true} onOpenChange={mockOnOpenChange} />
      </MockRouter>
    );
    
    // Rerender should not cause infinite loops
    expect(() => {
      rerender(
        <MockRouter>
          <CommandPalette open={true} onOpenChange={mockOnOpenChange} />
        </MockRouter>
      );
    }).not.toThrow();
  });

  it('should filter commands correctly even with undefined analytics', () => {
    const mockOnOpenChange = vi.fn();
    
    render(
      <MockRouter>
        <CommandPalette open={true} onOpenChange={mockOnOpenChange} />
      </MockRouter>
    );
    
    // Should show default commands
    expect(screen.getByText('Go to Home')).toBeInTheDocument();
    expect(screen.getByText('Go to Tools')).toBeInTheDocument();
  });
});
