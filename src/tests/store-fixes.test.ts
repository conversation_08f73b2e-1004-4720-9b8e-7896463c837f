import { describe, it, expect, beforeEach } from 'vitest';
import { useAppStore } from '@/stores/app-store';

describe('Store Fixes', () => {
  beforeEach(() => {
    // Reset store state before each test
    useAppStore.getState().clearAnalytics();
  });

  it('should handle trackEvent when analytics.events is undefined', () => {
    const store = useAppStore.getState();
    
    // Simulate the case where analytics.events might be undefined
    // This would happen during hydration from persistence
    expect(() => {
      store.trackEvent('test_event', { data: 'test' });
    }).not.toThrow();
    
    // Verify the event was tracked
    const events = store.analytics.events;
    expect(events).toBeDefined();
    expect(events.length).toBe(1);
    expect(events[0].type).toBe('test_event');
  });

  it('should handle adaptToUserBehavior when analytics.events is undefined', () => {
    const store = useAppStore.getState();
    
    // Clear events to simulate undefined state
    store.clearAnalytics();
    
    expect(() => {
      store.adaptToUserBehavior();
    }).not.toThrow();
  });

  it('should initialize analytics.events as empty array during hydration', () => {
    const store = useAppStore.getState();
    
    // Verify that analytics.events is always an array
    expect(Array.isArray(store.analytics.events)).toBe(true);
  });

  it('should handle multiple trackEvent calls without errors', () => {
    const store = useAppStore.getState();
    
    // Track multiple events
    for (let i = 0; i < 10; i++) {
      expect(() => {
        store.trackEvent(`test_event_${i}`, { index: i });
      }).not.toThrow();
    }
    
    // Verify all events were tracked
    expect(store.analytics.events.length).toBe(10);
  });

  it('should limit events to 1000 for performance', () => {
    const store = useAppStore.getState();
    
    // Track more than 1000 events
    for (let i = 0; i < 1100; i++) {
      store.trackEvent(`test_event_${i}`, { index: i });
    }
    
    // Verify events are limited to 1000
    expect(store.analytics.events.length).toBe(1000);
    
    // Verify the oldest events were removed (should start from event 100)
    expect(store.analytics.events[0].type).toBe('test_event_100');
  });
});
