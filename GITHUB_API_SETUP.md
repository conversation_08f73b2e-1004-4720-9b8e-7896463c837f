# GitHub API Setup Instructions

## Overview
The Trending AI Repositories section fetches the latest and most popular AI repositories from GitHub. To get the best experience with higher rate limits, you'll need to set up a GitHub Personal Access Token.

## Rate Limits
- **Without Token**: 60 requests per hour
- **With Token**: 5,000 requests per hour

## Setup Instructions

### Step 1: Create a GitHub Personal Access Token

1. **Go to GitHub Settings**
   - Navigate to [GitHub Settings](https://github.com/settings/tokens)
   - Or: GitHub Profile → Settings → Developer settings → Personal access tokens → Tokens (classic)

2. **Generate New Token**
   - Click "Generate new token (classic)"
   - Give it a descriptive name like "Sainpse Landing Page API"

3. **Set Token Permissions**
   - **Expiration**: Choose your preferred expiration (30 days, 60 days, 90 days, or no expiration)
   - **Scopes**: Select `public_repo` (this allows reading public repository information)
   - No other permissions are needed for this use case

4. **Generate and Copy Token**
   - Click "Generate token"
   - **IMPORTANT**: Copy the token immediately - you won't be able to see it again
   - It will look like: `ghp_1234567890abcdef1234567890abcdef12345678`

### Step 2: Configure Environment Variables

1. **Create .env file** (if it doesn't exist):
   ```bash
   touch .env
   ```

2. **Add your token to .env**:
   ```env
   VITE_GITHUB_TOKEN=your_actual_token_here
   ```
   
   Example:
   ```env
   VITE_GITHUB_TOKEN=ghp_1234567890abcdef1234567890abcdef12345678
   ```

3. **Make sure .env is in .gitignore**:
   ```gitignore
   .env
   .env.local
   .env.development.local
   .env.test.local
   .env.production.local
   ```

### Step 3: Restart Development Server

After adding the token, restart your development server:

```bash
npm run dev
# or
yarn dev
# or
bun dev
```

## Component Features

### What the Component Does
- Fetches trending AI repositories from GitHub
- Displays repositories with stars, forks, language, and descriptions
- Shows repository topics/tags
- Provides direct links to GitHub repositories
- Includes refresh functionality
- Responsive design with smooth animations

### Search Criteria
The component searches for repositories with these topics:
- `artificial-intelligence`
- `machine-learning`
- `deep-learning`
- `neural-networks`

### Error Handling
- Displays loading states
- Shows error messages if API fails
- Provides retry functionality
- Graceful fallback for missing data

## Customization Options

### Modify Search Query
Edit the `fetchTrendingRepos` function in `TrendingRepos.tsx`:

```typescript
const query = 'topic:artificial-intelligence OR topic:machine-learning OR topic:deep-learning OR topic:neural-networks';
```

### Change Number of Repositories
Modify the `per_page` parameter:

```typescript
const per_page = 6; // Change to desired number
```

### Add More Topics
You can add more topics to the search:

```typescript
const query = 'topic:artificial-intelligence OR topic:machine-learning OR topic:tensorflow OR topic:pytorch';
```

### Sorting Options
Change the sorting criteria:

```typescript
const sort = 'stars'; // Options: 'stars', 'forks', 'updated'
const order = 'desc'; // Options: 'desc', 'asc'
```

## Production Deployment

### Environment Variables for Production
When deploying to production platforms, add the environment variable:

**Vercel:**
```bash
vercel env add VITE_GITHUB_TOKEN
```

**Netlify:**
- Go to Site Settings → Environment variables
- Add: `VITE_GITHUB_TOKEN` = `your_token_here`

**Other Platforms:**
- Add `VITE_GITHUB_TOKEN` to your platform's environment variables

### Security Considerations
- Never commit the actual token to version control
- Use different tokens for development and production
- Regularly rotate tokens for security
- The token only needs `public_repo` permissions

## Troubleshooting

### Common Issues

1. **Rate Limit Exceeded**
   - Solution: Add GitHub token or wait for rate limit reset

2. **Token Not Working**
   - Check if token is correctly formatted
   - Ensure token has `public_repo` permissions
   - Verify token hasn't expired

3. **API Request Fails**
   - Check network connectivity
   - Verify GitHub API is accessible
   - Check browser console for detailed error messages

### Testing Without Token
The component will work without a token but with limited rate limits. For development, you can test the functionality and add the token later for production use.

## Component Integration

The component has been integrated into your landing page:
- Added to navigation menu as "AI Trends"
- Positioned between Services and Values sections
- Fully responsive and matches your design system
- Uses your existing color scheme and animations

## Support

If you encounter any issues:
1. Check the browser console for error messages
2. Verify your token permissions on GitHub
3. Ensure the token is correctly set in environment variables
4. Try the refresh button in the component
