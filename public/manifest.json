{"name": "Sainpse Institute of Augmented Intelligence", "short_name": "Sainpse Portal", "description": "Advanced AI solutions and educational resources for the future of technology", "start_url": "/", "scope": "/", "display": "standalone", "orientation": "portrait-primary", "theme_color": "#10b981", "background_color": "#08080c", "lang": "en", "dir": "ltr", "icons": [{"src": "/favicon-16x16.png", "sizes": "16x16", "type": "image/png"}, {"src": "/favicon-32x32.png", "sizes": "32x32", "type": "image/png"}, {"src": "/android-chrome-192x192.png", "sizes": "192x192", "type": "image/png", "purpose": "any maskable"}, {"src": "/android-chrome-512x512.png", "sizes": "512x512", "type": "image/png", "purpose": "any maskable"}, {"src": "/apple-touch-icon.png", "sizes": "180x180", "type": "image/png"}], "categories": ["education", "productivity", "business", "developer"], "screenshots": [{"src": "/og-image.png", "sizes": "1200x630", "type": "image/png", "platform": "wide", "label": "Sainpse Portal Homepage"}], "shortcuts": [{"name": "Education Hub", "short_name": "Education", "description": "Access learning resources and courses", "url": "/education", "icons": [{"src": "/android-chrome-192x192.png", "sizes": "192x192", "type": "image/png"}]}, {"name": "AI Tools", "short_name": "Tools", "description": "Explore our AI-powered tools", "url": "/tools", "icons": [{"src": "/android-chrome-192x192.png", "sizes": "192x192", "type": "image/png"}]}, {"name": "Services", "short_name": "Services", "description": "View our professional services", "url": "/#services", "icons": [{"src": "/android-chrome-192x192.png", "sizes": "192x192", "type": "image/png"}]}], "related_applications": [], "prefer_related_applications": false, "edge_side_panel": {"preferred_width": 400}, "launch_handler": {"client_mode": "navigate-existing"}, "handle_links": "preferred", "protocol_handlers": [{"protocol": "web+sainpse", "url": "/?handler=%s"}], "file_handlers": [{"action": "/tools", "accept": {"application/json": [".json"], "text/csv": [".csv"], "text/plain": [".txt"]}}]}